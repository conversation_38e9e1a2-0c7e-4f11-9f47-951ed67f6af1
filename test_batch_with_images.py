#!/usr/bin/env python3
"""
测试批量处理中的图片复制功能
"""

import os
import time
from pathlib import Path
from table_annotation_optimizer import BatchProcessor, CONFIG

def test_small_batch():
    """测试小批量处理"""
    print("=" * 60)
    print("测试小批量处理（包含图片复制）")
    print("=" * 60)
    
    # 创建测试配置
    test_config = CONFIG.copy()
    test_config['max_workers'] = 1  # 单线程处理
    
    print(f"输入目录: {test_config['input_dir']}")
    print(f"输出目录: {test_config['output_dir']}")
    print(f"复制图片: {test_config.get('copy_images', True)}")
    
    # 创建处理器
    processor = BatchProcessor(test_config)
    
    # 查找标注文件
    annotation_files = processor.find_annotation_files(test_config['input_dir'])
    
    if not annotation_files:
        print("❌ 未找到标注文件")
        return
    
    print(f"找到 {len(annotation_files)} 个标注文件")
    
    # 只处理前3个文件进行测试
    test_files = annotation_files[:3]
    print(f"测试处理前 {len(test_files)} 个文件")
    
    # 手动处理这些文件
    from table_annotation_optimizer import PerspectiveAwareOptimizer
    
    optimizer = PerspectiveAwareOptimizer(
        tolerance=test_config['tolerance'],
        preserve_perspective=test_config['preserve_perspective'],
        adaptive_threshold=test_config['adaptive_threshold'],
        quality_aware=test_config['quality_aware'],
        angle_correction=test_config['angle_correction'],
        conservative_mode=test_config['conservative_mode']
    )
    
    output_dir = test_config['output_dir']
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    stats = {
        'total_files': len(test_files),
        'successful': 0,
        'failed': 0,
        'images_copied': 0,
        'images_failed': 0
    }
    
    for i, annotation_file in enumerate(test_files):
        print(f"\n[{i+1:2d}/{len(test_files)}] 处理: {annotation_file.name}")
        
        try:
            # 查找对应的图片文件
            image_file = processor.find_corresponding_image(annotation_file)
            
            # 生成输出文件路径
            output_file = str(annotation_file).replace(
                test_config['input_dir'], 
                test_config['output_dir']
            ).replace('.json', '_aligned.json')
            
            # 执行优化
            result = optimizer.optimize_table_annotation(
                str(annotation_file),
                output_file,
                image_file
            )
            
            if result['success']:
                stats['successful'] += 1
                print(f"    ✅ 优化成功")
                
                # 复制对应的图片文件（如果启用）
                if test_config.get('copy_images', True):
                    if image_file:
                        if processor.copy_image_file(image_file, output_dir):
                            stats['images_copied'] += 1
                            print(f"    📷 图片已复制: {Path(image_file).name}")
                        else:
                            stats['images_failed'] += 1
                            print(f"    ❌ 图片复制失败: {Path(image_file).name}")
                    else:
                        stats['images_failed'] += 1
                        print(f"    ⚠️  未找到对应图片")
            else:
                stats['failed'] += 1
                print(f"    ❌ 优化失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            stats['failed'] += 1
            print(f"    ❌ 处理出错: {e}")
    
    # 打印统计结果
    print("\n" + "=" * 60)
    print("处理统计")
    print("=" * 60)
    print(f"总文件数: {stats['total_files']}")
    print(f"成功处理: {stats['successful']}")
    print(f"处理失败: {stats['failed']}")
    print(f"图片复制成功: {stats['images_copied']}")
    print(f"图片复制失败: {stats['images_failed']}")
    
    # 检查输出目录
    output_path = Path(output_dir)
    if output_path.exists():
        json_files = list(output_path.glob("*_aligned.json"))
        image_files = []
        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
            image_files.extend(list(output_path.glob(f"*{ext}")))
        
        print(f"\n输出目录检查:")
        print(f"JSON文件数量: {len(json_files)}")
        print(f"图片文件数量: {len(image_files)}")
        
        if len(image_files) == stats['images_copied']:
            print("✅ 图片复制数量正确")
        else:
            print(f"⚠️  图片复制数量不匹配: 预期 {stats['images_copied']}, 实际 {len(image_files)}")

def check_existing_output():
    """检查现有输出目录的内容"""
    print("\n" + "=" * 60)
    print("检查现有输出目录")
    print("=" * 60)
    
    output_dir = Path(CONFIG['output_dir'])
    
    if not output_dir.exists():
        print(f"输出目录不存在: {output_dir}")
        return
    
    print(f"输出目录: {output_dir}")
    
    # 统计文件
    json_files = list(output_dir.glob("*.json"))
    image_files = []
    for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
        image_files.extend(list(output_dir.glob(f"*{ext}")))
    
    print(f"JSON文件数量: {len(json_files)}")
    print(f"图片文件数量: {len(image_files)}")
    
    # 检查配对情况
    json_bases = set()
    for json_file in json_files:
        base_name = json_file.stem
        if base_name.endswith('_aligned'):
            base_name = base_name[:-8]  # 移除 '_aligned'
        if base_name.endswith('_table_annotation'):
            base_name = base_name[:-17]  # 移除 '_table_annotation'
        json_bases.add(base_name)
    
    image_bases = set()
    for image_file in image_files:
        image_bases.add(image_file.stem)
    
    matched = json_bases & image_bases
    json_only = json_bases - image_bases
    image_only = image_bases - json_bases
    
    print(f"\n配对分析:")
    print(f"JSON和图片都有: {len(matched)}")
    print(f"只有JSON: {len(json_only)}")
    print(f"只有图片: {len(image_only)}")
    
    if json_only:
        print(f"\n缺少图片的文件（前10个）:")
        for base_name in list(json_only)[:10]:
            print(f"  - {base_name}")
    
    if image_only:
        print(f"\n缺少JSON的图片（前10个）:")
        for base_name in list(image_only)[:10]:
            print(f"  - {base_name}")

if __name__ == "__main__":
    print("测试批量处理中的图片复制功能")
    print("=" * 80)
    
    # 1. 检查现有输出
    check_existing_output()
    
    # 2. 测试小批量处理
    test_small_batch()
    
    print("\n" + "=" * 80)
    print("测试完成！")
    print("=" * 80)
