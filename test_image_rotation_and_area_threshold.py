#!/usr/bin/env python3
"""
测试图片旋转修复和表格面积比例阈值计算的改进效果
"""

import os
import json
import math
from pathlib import Path
from PIL import Image, ImageOps
from table_annotation_optimizer import PerspectiveAwareOptimizer

def test_image_rotation_fix():
    """测试图片旋转修复功能"""
    print("=" * 60)
    print("测试图片旋转修复功能")
    print("=" * 60)
    
    # 查找一些测试图片
    test_dir = Path("organized_dataset")
    if not test_dir.exists():
        print("未找到测试目录 organized_dataset")
        return
    
    image_files = list(test_dir.glob("*.jpg"))[:3]  # 取前3个图片测试
    if not image_files:
        image_files = list(test_dir.glob("*.png"))[:3]
    
    if not image_files:
        print("未找到测试图片文件")
        return
    
    print(f"找到 {len(image_files)} 个测试图片")
    
    # 创建测试输出目录
    test_output_dir = Path("test_rotation_fix")
    test_output_dir.mkdir(exist_ok=True)
    
    optimizer = PerspectiveAwareOptimizer()
    
    for i, image_file in enumerate(image_files):
        print(f"\n测试图片 {i+1}: {image_file.name}")
        
        try:
            # 检查原始图片的EXIF信息
            with Image.open(image_file) as img:
                original_size = img.size
                has_exif = hasattr(img, '_getexif') and img._getexif() is not None
                
                # 检查是否有旋转信息
                exif_transpose_img = ImageOps.exif_transpose(img)
                has_rotation = exif_transpose_img is not img
                
                print(f"  原始尺寸: {original_size}")
                print(f"  包含EXIF: {has_exif}")
                print(f"  需要旋转修正: {has_rotation}")
                
                if has_rotation:
                    corrected_size = exif_transpose_img.size
                    print(f"  修正后尺寸: {corrected_size}")
            
            # 测试复制功能
            success = optimizer.copy_image_file(str(image_file), str(test_output_dir))
            
            if success:
                output_file = test_output_dir / image_file.name
                if output_file.exists():
                    # 检查输出图片
                    with Image.open(output_file) as output_img:
                        output_size = output_img.size
                        print(f"  输出尺寸: {output_size}")
                        
                        # 检查是否还有旋转信息
                        output_exif_transpose = ImageOps.exif_transpose(output_img)
                        still_has_rotation = output_exif_transpose is not output_img
                        print(f"  输出仍需旋转: {still_has_rotation}")
                        
                        if not still_has_rotation:
                            print("  ✅ 旋转问题已修复")
                        else:
                            print("  ⚠️  旋转问题仍存在")
                else:
                    print("  ❌ 输出文件不存在")
            else:
                print("  ❌ 复制失败")
                
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")

def test_area_ratio_threshold():
    """测试表格面积比例阈值计算"""
    print("\n" + "=" * 60)
    print("测试表格面积比例阈值计算")
    print("=" * 60)
    
    # 查找测试文件
    test_dir = Path("organized_dataset")
    if not test_dir.exists():
        print("未找到测试目录 organized_dataset")
        return
    
    annotation_files = list(test_dir.glob("*_table_annotation.json"))[:5]  # 取前5个文件测试
    
    if not annotation_files:
        print("未找到标注文件")
        return
    
    print(f"找到 {len(annotation_files)} 个测试文件")
    
    # 配置优化器
    optimizer = PerspectiveAwareOptimizer(
        tolerance=3.0,
        adaptive_threshold=True,  # 启用自适应阈值
        preserve_perspective=True,
        quality_aware=False
    )
    
    results = []
    
    for i, annotation_file in enumerate(annotation_files):
        print(f"\n测试文件 {i+1}: {annotation_file.name}")
        print("-" * 40)
        
        try:
            # 查找对应的图片文件
            base_name = annotation_file.stem.replace('_table_annotation', '')
            image_file = None
            for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                img_path = annotation_file.parent / f"{base_name}{ext}"
                if img_path.exists():
                    image_file = str(img_path)
                    break
            
            if not image_file:
                print("  未找到对应的图片文件")
                continue
            
            # 加载标注数据（这会触发阈值计算）
            optimizer.load_annotation(str(annotation_file), image_file)
            
            # 收集计算结果
            result = {
                'file': annotation_file.name,
                'image_file': Path(image_file).name,
                'final_threshold': optimizer.tolerance,
                'base_threshold': optimizer.base_tolerance
            }
            
            if hasattr(optimizer, 'area_ratio_info'):
                result['area_info'] = optimizer.area_ratio_info
            
            if hasattr(optimizer, 'threshold_calculation_info'):
                result['calc_info'] = optimizer.threshold_calculation_info
            
            if hasattr(optimizer, 'image_info'):
                result['image_info'] = optimizer.image_info
            
            results.append(result)
            
            # 分析结果
            threshold_change = optimizer.tolerance / optimizer.base_tolerance
            print(f"  阈值变化: {optimizer.base_tolerance:.2f} → {optimizer.tolerance:.2f} (倍数: {threshold_change:.2f})")
            
            if hasattr(optimizer, 'area_ratio_info'):
                area_info = optimizer.area_ratio_info
                print(f"  表格面积占比: {area_info['area_ratio']:.1%}")
                
                if area_info['area_ratio'] > 0.8:
                    print("    📊 大面积表格 - 使用更精确的阈值")
                elif area_info['area_ratio'] > 0.5:
                    print("    📊 中等面积表格 - 使用标准阈值")
                else:
                    print("    📊 小面积表格 - 使用更宽松的阈值")
            
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
    
    # 生成分析报告
    if results:
        print("\n" + "=" * 60)
        print("阈值计算分析报告")
        print("=" * 60)
        
        # 按面积比例分组分析
        large_area_files = [r for r in results if r.get('area_info', {}).get('area_ratio', 0) > 0.8]
        medium_area_files = [r for r in results if 0.5 < r.get('area_info', {}).get('area_ratio', 0) <= 0.8]
        small_area_files = [r for r in results if r.get('area_info', {}).get('area_ratio', 0) <= 0.5]
        
        print(f"大面积表格文件 (>80%): {len(large_area_files)}")
        print(f"中等面积表格文件 (50%-80%): {len(medium_area_files)}")
        print(f"小面积表格文件 (≤50%): {len(small_area_files)}")
        
        # 计算各组的平均阈值倍数
        if large_area_files:
            large_avg_multiplier = sum(r['final_threshold'] / r['base_threshold'] for r in large_area_files) / len(large_area_files)
            print(f"大面积表格平均阈值倍数: {large_avg_multiplier:.2f}")
        
        if medium_area_files:
            medium_avg_multiplier = sum(r['final_threshold'] / r['base_threshold'] for r in medium_area_files) / len(medium_area_files)
            print(f"中等面积表格平均阈值倍数: {medium_avg_multiplier:.2f}")
        
        if small_area_files:
            small_avg_multiplier = sum(r['final_threshold'] / r['base_threshold'] for r in small_area_files) / len(small_area_files)
            print(f"小面积表格平均阈值倍数: {small_avg_multiplier:.2f}")
        
        # 保存详细结果
        with open('area_ratio_threshold_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n详细分析结果已保存到: area_ratio_threshold_analysis.json")

def analyze_threshold_effectiveness():
    """分析阈值计算的有效性"""
    print("\n" + "=" * 60)
    print("阈值计算有效性分析")
    print("=" * 60)
    
    # 加载分析结果
    results_file = Path("area_ratio_threshold_analysis.json")
    if not results_file.exists():
        print("未找到分析结果文件，请先运行面积比例测试")
        return
    
    with open(results_file, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    print(f"分析 {len(results)} 个文件的阈值计算结果")
    
    # 统计分析
    threshold_multipliers = [r['final_threshold'] / r['base_threshold'] for r in results]
    area_ratios = [r.get('area_info', {}).get('area_ratio', 0) for r in results]
    
    print(f"\n阈值倍数统计:")
    print(f"  最小倍数: {min(threshold_multipliers):.2f}")
    print(f"  最大倍数: {max(threshold_multipliers):.2f}")
    print(f"  平均倍数: {sum(threshold_multipliers) / len(threshold_multipliers):.2f}")
    
    print(f"\n面积比例统计:")
    print(f"  最小比例: {min(area_ratios):.1%}")
    print(f"  最大比例: {max(area_ratios):.1%}")
    print(f"  平均比例: {sum(area_ratios) / len(area_ratios):.1%}")
    
    # 分析相关性
    if len(area_ratios) > 1:
        # 简单的相关性分析
        mean_area = sum(area_ratios) / len(area_ratios)
        mean_multiplier = sum(threshold_multipliers) / len(threshold_multipliers)
        
        covariance = sum((area_ratios[i] - mean_area) * (threshold_multipliers[i] - mean_multiplier) 
                        for i in range(len(area_ratios))) / len(area_ratios)
        
        area_variance = sum((a - mean_area) ** 2 for a in area_ratios) / len(area_ratios)
        multiplier_variance = sum((m - mean_multiplier) ** 2 for m in threshold_multipliers) / len(threshold_multipliers)
        
        if area_variance > 0 and multiplier_variance > 0:
            correlation = covariance / math.sqrt(area_variance * multiplier_variance)
            print(f"\n面积比例与阈值倍数的相关性: {correlation:.3f}")
            
            if correlation < -0.5:
                print("  ✅ 强负相关 - 面积越大，阈值倍数越小（符合预期）")
            elif correlation < -0.3:
                print("  ✅ 中等负相关 - 算法按预期工作")
            elif correlation < 0.3:
                print("  ⚠️  弱相关 - 算法效果一般")
            else:
                print("  ❌ 正相关或无相关 - 算法可能有问题")

if __name__ == "__main__":
    print("测试图片旋转修复和表格面积比例阈值计算改进")
    print("=" * 80)
    
    # 测试1: 图片旋转修复
    test_image_rotation_fix()
    
    # 测试2: 表格面积比例阈值计算
    test_area_ratio_threshold()
    
    # 测试3: 阈值计算有效性分析
    analyze_threshold_effectiveness()
    
    print("\n" + "=" * 80)
    print("所有测试完成！")
    print("=" * 80)
