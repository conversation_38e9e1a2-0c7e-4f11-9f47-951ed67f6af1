#!/usr/bin/env python3
"""
测试图片复制功能是否正常工作
"""

import os
import json
from pathlib import Path
from table_annotation_optimizer import BatchProcessor, CONFIG

def test_image_copy_functionality():
    """测试图片复制功能"""
    print("=" * 60)
    print("测试图片复制功能")
    print("=" * 60)
    
    # 使用当前配置
    input_dir = CONFIG['input_dir']
    output_dir = CONFIG['output_dir']
    
    print(f"输入目录: {input_dir}")
    print(f"输出目录: {output_dir}")
    print(f"复制图片设置: {CONFIG.get('copy_images', True)}")
    
    # 检查输入目录是否存在
    if not Path(input_dir).exists():
        print(f"❌ 输入目录不存在: {input_dir}")
        return
    
    # 创建批量处理器
    processor = BatchProcessor(CONFIG)
    
    # 查找标注文件
    annotation_files = processor.find_annotation_files(input_dir)
    print(f"\n找到 {len(annotation_files)} 个标注文件")
    
    if not annotation_files:
        print("❌ 未找到标注文件")
        return
    
    # 测试前几个文件的图片查找和复制
    test_files = annotation_files[:3]  # 测试前3个文件
    
    print("\n测试图片查找和复制:")
    print("-" * 40)
    
    for i, annotation_file in enumerate(test_files):
        print(f"\n{i+1}. 测试文件: {annotation_file.name}")
        
        # 查找对应的图片文件
        image_file = processor.find_corresponding_image(annotation_file)
        
        if image_file:
            print(f"   ✅ 找到图片: {Path(image_file).name}")
            print(f"   📁 图片路径: {image_file}")
            
            # 检查图片文件是否真的存在
            if Path(image_file).exists():
                print(f"   ✅ 图片文件存在")
                
                # 测试复制功能
                try:
                    success = processor.copy_image_file(image_file, output_dir)
                    if success:
                        print(f"   ✅ 图片复制成功")
                        
                        # 检查输出文件是否存在
                        output_image_path = Path(output_dir) / Path(image_file).name
                        if output_image_path.exists():
                            print(f"   ✅ 输出图片存在: {output_image_path}")
                            
                            # 比较文件大小
                            original_size = Path(image_file).stat().st_size
                            output_size = output_image_path.stat().st_size
                            print(f"   📊 原始大小: {original_size:,} 字节")
                            print(f"   📊 输出大小: {output_size:,} 字节")
                            
                            if abs(original_size - output_size) < 1000:  # 允许1KB的差异
                                print(f"   ✅ 文件大小基本一致")
                            else:
                                print(f"   ⚠️  文件大小差异较大")
                        else:
                            print(f"   ❌ 输出图片不存在: {output_image_path}")
                    else:
                        print(f"   ❌ 图片复制失败")
                        
                except Exception as e:
                    print(f"   ❌ 复制过程出错: {e}")
            else:
                print(f"   ❌ 图片文件不存在: {image_file}")
        else:
            print(f"   ❌ 未找到对应图片")

def test_batch_processing():
    """测试完整的批量处理流程"""
    print("\n" + "=" * 60)
    print("测试完整批量处理流程")
    print("=" * 60)
    
    # 创建一个测试配置，只处理1个文件
    test_config = CONFIG.copy()
    test_config['max_workers'] = 1
    
    processor = BatchProcessor(test_config)
    
    # 查找标注文件
    annotation_files = processor.find_annotation_files(test_config['input_dir'])
    
    if not annotation_files:
        print("❌ 未找到标注文件")
        return
    
    print(f"找到 {len(annotation_files)} 个标注文件")
    
    # 只处理第一个文件进行测试
    test_file = annotation_files[0]
    print(f"测试处理: {test_file.name}")
    
    # 检查处理前输出目录的图片数量
    output_dir = Path(test_config['output_dir'])
    if output_dir.exists():
        before_images = list(output_dir.glob("*.jpg")) + list(output_dir.glob("*.png")) + list(output_dir.glob("*.jpeg"))
        print(f"处理前输出目录图片数量: {len(before_images)}")
    else:
        before_images = []
        print("输出目录不存在，将会创建")
    
    # 执行批量处理（只处理一个文件）
    try:
        # 模拟单个文件的处理过程
        from table_annotation_optimizer import PerspectiveAwareOptimizer
        
        optimizer = PerspectiveAwareOptimizer(
            tolerance=test_config['tolerance'],
            preserve_perspective=test_config['preserve_perspective'],
            adaptive_threshold=test_config['adaptive_threshold'],
            quality_aware=test_config['quality_aware'],
            angle_correction=test_config['angle_correction'],
            conservative_mode=test_config['conservative_mode']
        )
        
        # 查找对应的图片文件
        image_file = processor.find_corresponding_image(test_file)
        
        # 执行优化
        output_file = str(test_file).replace('.json', '_aligned.json')
        output_file = output_file.replace(test_config['input_dir'], test_config['output_dir'])
        
        result = optimizer.optimize_table_annotation(
            str(test_file),
            output_file,
            image_file
        )
        
        print(f"优化结果: {'成功' if result['success'] else '失败'}")
        
        if result['success']:
            # 检查图片是否被复制
            if image_file and test_config.get('copy_images', True):
                image_name = Path(image_file).name
                output_image_path = output_dir / image_name
                
                if output_image_path.exists():
                    print(f"✅ 图片已复制: {image_name}")
                else:
                    print(f"❌ 图片未复制: {image_name}")
                    
                    # 手动尝试复制
                    print("尝试手动复制...")
                    success = processor.copy_image_file(image_file, str(output_dir))
                    if success:
                        print("✅ 手动复制成功")
                    else:
                        print("❌ 手动复制也失败")
        
    except Exception as e:
        print(f"❌ 处理过程出错: {e}")
        import traceback
        traceback.print_exc()

def check_configuration():
    """检查配置是否正确"""
    print("\n" + "=" * 60)
    print("检查配置")
    print("=" * 60)
    
    print(f"输入目录: {CONFIG['input_dir']}")
    print(f"输出目录: {CONFIG['output_dir']}")
    print(f"复制图片: {CONFIG.get('copy_images', True)}")
    print(f"图片扩展名: {CONFIG.get('image_extensions', [])}")
    print(f"标注文件模式: {CONFIG.get('annotation_pattern', '')}")
    
    # 检查目录
    input_path = Path(CONFIG['input_dir'])
    output_path = Path(CONFIG['output_dir'])
    
    print(f"\n目录检查:")
    print(f"输入目录存在: {input_path.exists()}")
    print(f"输出目录存在: {output_path.exists()}")
    
    if input_path.exists():
        annotation_files = list(input_path.glob(CONFIG.get('annotation_pattern', '*_table_annotation.json')))
        image_files = []
        for ext in CONFIG.get('image_extensions', ['.jpg', '.jpeg', '.png']):
            image_files.extend(list(input_path.glob(f"*{ext}")))
        
        print(f"输入目录中的标注文件数量: {len(annotation_files)}")
        print(f"输入目录中的图片文件数量: {len(image_files)}")

if __name__ == "__main__":
    print("测试图片复制功能修复")
    print("=" * 80)
    
    # 1. 检查配置
    check_configuration()
    
    # 2. 测试图片复制功能
    test_image_copy_functionality()
    
    # 3. 测试完整批量处理
    test_batch_processing()
    
    print("\n" + "=" * 80)
    print("测试完成！")
    print("=" * 80)
