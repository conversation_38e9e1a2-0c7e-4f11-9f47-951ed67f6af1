{"table_ind": 0, "image_path": "", "type": 1, "cells": [{"cell_ind": 0, "header": false, "content": [{"bbox": null, "direction": null, "text": "无", "score": null}], "bbox": {"p1": [160.1841077600133, 419.9454149520689], "p2": [559.2195857458529, 419.9454149520689], "p3": [554.2385127209576, 650.0352980655066], "p4": [146.90393222551248, 639.3272900356518]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 1, "header": false, "content": [{"bbox": null, "direction": null, "text": "网页中所有DOM结构绘制完毕后就执行，可能DOM\n元素关联的东西并没有加载完", "score": null}], "bbox": {"p1": [568.0822478261614, 80.39876408467173], "p2": [1003.4577026367185, 69.87617903217344], "p3": [1003.4577026367185, 141.74497254251492], "p4": [568.0822478261614, 141.74497254251492]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 2, "header": true, "content": [{"bbox": null, "direction": null, "text": "$(document).ready()", "score": null}], "bbox": {"p1": [568.0822478261614, 41.37250900268555], "p2": [1003.4577026367185, 33.876193306560104], "p3": [1003.4577026367185, 85.23753815483934], "p4": [568.0822478261614, 85.23753815483934]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 3, "header": false, "content": [{"bbox": null, "direction": null, "text": "必须等待网页中所有的内容加载完毕后 （包括\n图片）才能执行", "score": null}], "bbox": {"p1": [181.2482938730948, 85.23753815483936], "p2": [568.0822478261614, 80.39876408467173], "p3": [568.0822478261614, 141.74497254251492], "p4": [181.24829387309484, 141.74497254251492]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 4, "header": false, "content": [{"bbox": null, "direction": null, "text": "不能同时编写多个\n一下代码无法正确执行：window. onload = function(){\nalert(\"testl\")\n} :\nwindow. onload = function(){\nalert(\"test2\")\n} :\n结果只会输出 “test2”", "score": null}], "bbox": {"p1": [170.63609547370953, 138.89593118530934], "p2": [568.0822478261614, 138.89593118530934], "p3": [559.2195857458529, 419.9454149520689], "p4": [160.1841077600133, 419.9454149520689]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 5, "header": false, "content": [{"bbox": null, "direction": null, "text": "执行时机", "score": null}], "bbox": {"p1": [48.1650910376378, 85.23753815483934], "p2": [181.2482938730948, 85.23753815483936], "p3": [181.24829387309484, 141.74497254251492], "p4": [43.77605400926006, 141.74497254251492]}, "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 6, "header": true, "content": [{"bbox": null, "direction": null, "text": "window. onload", "score": null}], "bbox": {"p1": [181.2482938730948, 48.580608586560935], "p2": [568.0822478261614, 44.65050629475005], "p3": [568.0822478261614, 85.23753815483934], "p4": [181.2482938730948, 85.23753815483934]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 7, "header": false, "content": [{"bbox": null, "direction": null, "text": "$(document) . ready(function(){\n//...\n}):\n可以简写成：\n$(function(){\n//...\n}):", "score": null}], "bbox": {"p1": [559.2195857458529, 419.9454149520689], "p2": [1003.4577026367185, 427.46794899359116], "p3": [1003.4577026367185, 661.0487369885255], "p4": [554.2385127209576, 650.0352980655066]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 8, "header": false, "content": [{"bbox": null, "direction": null, "text": "简化写法", "score": null}], "bbox": {"p1": [22.235247475921003, 414.64045180122673], "p2": [160.1841077600133, 419.9454149520689], "p3": [146.90393222551248, 639.3272900356518], "p4": [4.910692231795778, 635.7201526457484]}, "lloc": {"start_row": 3, "end_row": 3, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 9, "header": true, "content": [{"bbox": null, "direction": null, "text": "", "score": null}], "bbox": {"p1": [50.962902698335995, 49.104225158691406], "p2": [181.2482938730948, 48.580608586560935], "p3": [181.2482938730948, 85.23753815483934], "p4": [48.1650910376378, 85.23753815483934]}, "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 10, "header": false, "content": [{"bbox": null, "direction": null, "text": "能同时编写多个\n以下代码正确执行:\n$(document) . ready(function(){\nalert(\"Hello World!\");\n});\n$(document) . ready(function(){\nalert(\"Hello World!\");\n});结果两次都输出", "score": null}], "bbox": {"p1": [568.0822478261614, 133.19784847089818], "p2": [1003.4577026367185, 133.19784847089818], "p3": [1003.4577026367185, 427.46794899359116], "p4": [559.2195857458529, 419.9454149520689]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 2, "end_col": 2}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}, {"cell_ind": 11, "header": false, "content": [{"bbox": null, "direction": null, "text": "编写个数", "score": null}], "bbox": {"p1": [43.77605400926006, 138.89593118530934], "p2": [181.24829387309484, 138.89593118530934], "p3": [160.1841077600133, 419.9454149520689], "p4": [22.235247475921003, 414.64045180122673]}, "lloc": {"start_row": 2, "end_row": 2, "start_col": 0, "end_col": 0}, "border": {"style": {"top": 1, "right": 1, "bottom": 1, "left": 1}, "width": null, "color": null}}]}