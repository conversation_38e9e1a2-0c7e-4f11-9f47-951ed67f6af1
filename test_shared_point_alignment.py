#!/usr/bin/env python3
"""
测试共享角点精确对齐功能

专门测试相邻单元格是否能够精确共享同一个坐标
"""

import os
import json
import math
import time
from pathlib import Path
from table_annotation_optimizer import PerspectiveAwareOptimizer


def analyze_shared_point_precision(cells):
    """
    分析共享点的精确度
    
    Args:
        cells: 单元格列表
        
    Returns:
        共享点精确度统计
    """
    # 收集所有角点
    all_points = []
    for cell_idx, cell in enumerate(cells):
        bbox = cell.get('bbox', {})
        for point_name in ['p1', 'p2', 'p3', 'p4']:
            if point_name in bbox:
                coords = bbox[point_name]
                all_points.append({
                    'cell_idx': cell_idx,
                    'point_name': point_name,
                    'x': coords[0],
                    'y': coords[1],
                    'coords': coords
                })
    
    # 使用距离聚类找到共享点组
    shared_groups = []
    used_indices = set()
    cluster_threshold = 2.0  # 2像素内认为是共享点
    
    for i, point1 in enumerate(all_points):
        if i in used_indices:
            continue
            
        group = [point1]
        used_indices.add(i)
        
        for j, point2 in enumerate(all_points):
            if j in used_indices:
                continue
                
            distance = math.sqrt((point1['x'] - point2['x'])**2 + 
                               (point1['y'] - point2['y'])**2)
            
            if distance <= cluster_threshold:
                group.append(point2)
                used_indices.add(j)
        
        if len(group) > 1:  # 只关心真正共享的点
            shared_groups.append(group)
    
    # 分析每个共享组的精确度
    precision_stats = {
        'total_shared_groups': len(shared_groups),
        'total_shared_points': sum(len(group) for group in shared_groups),
        'max_deviation': 0.0,
        'avg_deviation': 0.0,
        'precise_groups': 0,  # 偏差小于0.5像素的组
        'group_details': []
    }
    
    total_deviation = 0.0
    
    for group in shared_groups:
        if len(group) < 2:
            continue
            
        # 计算组内中心点
        center_x = sum(p['x'] for p in group) / len(group)
        center_y = sum(p['y'] for p in group) / len(group)
        
        # 计算每个点到中心的距离
        deviations = []
        for point in group:
            deviation = math.sqrt((point['x'] - center_x)**2 + 
                                (point['y'] - center_y)**2)
            deviations.append(deviation)
        
        max_group_deviation = max(deviations)
        avg_group_deviation = sum(deviations) / len(deviations)
        
        precision_stats['max_deviation'] = max(precision_stats['max_deviation'], max_group_deviation)
        total_deviation += avg_group_deviation
        
        # 记录组详情
        group_detail = {
            'size': len(group),
            'max_deviation': max_group_deviation,
            'avg_deviation': avg_group_deviation,
            'center': [center_x, center_y],
            'is_precise': max_group_deviation < 0.5
        }
        precision_stats['group_details'].append(group_detail)
        
        if group_detail['is_precise']:
            precision_stats['precise_groups'] += 1
    
    if shared_groups:
        precision_stats['avg_deviation'] = total_deviation / len(shared_groups)
    
    return precision_stats


def test_shared_point_alignment():
    """测试共享点对齐功能"""
    print("🎯 共享角点精确对齐测试")
    print("=" * 60)
    print("测试目标：确保相邻单元格精确共享同一个坐标")
    print("=" * 60)
    
    # 选择测试文件
    test_files = list(Path("borderless_table").glob("*.json"))[:3]
    
    if not test_files:
        print("❌ 未找到测试文件")
        return
    
    results = []
    
    for test_file in test_files:
        print(f"\n测试样本: {test_file.name}")
        print("-" * 50)
        
        # 读取原始数据
        try:
            with open(test_file, 'r', encoding='utf-8') as f:
                original_data = json.load(f)
            original_cells = original_data.get('cells', [])
            
            if not original_cells:
                print("  ❌ 无单元格数据")
                continue
                
            print(f"  原始单元格数: {len(original_cells)}")
            
        except Exception as e:
            print(f"  ❌ 读取失败: {e}")
            continue
        
        # 分析原始共享点精确度
        print("\n📊 原始共享点精确度:")
        original_precision = analyze_shared_point_precision(original_cells)
        print(f"  共享点组数: {original_precision['total_shared_groups']}")
        print(f"  共享点总数: {original_precision['total_shared_points']}")
        print(f"  最大偏差: {original_precision['max_deviation']:.2f}px")
        print(f"  平均偏差: {original_precision['avg_deviation']:.2f}px")
        print(f"  精确组数: {original_precision['precise_groups']}/{original_precision['total_shared_groups']}")
        
        # 查找对应图片
        image_file = None
        for ext in ['.jpg', '.jpeg', '.png']:
            img_path = test_file.parent / f"{test_file.stem.replace('_table_annotation', '')}{ext}"
            if img_path.exists():
                image_file = str(img_path)
                break
        
        # 测试改进的共享点对齐
        print("\n🔧 改进的共享点对齐:")
        start_time = time.time()
        
        optimizer = PerspectiveAwareOptimizer(
            tolerance=1.5,              # 保守阈值
            preserve_perspective=True,
            adaptive_threshold=False,
            quality_aware=False,
            angle_correction=False,
            conservative_mode=True      # 保守模式
        )
        
        output_file = f"shared_aligned_{test_file.name}"
        result = optimizer.optimize_table_annotation(
            str(test_file),
            output_file,
            image_file
        )
        
        processing_time = time.time() - start_time
        
        if result['success']:
            print(f"  ✅ 处理成功，耗时: {processing_time:.3f}秒")
            
            # 分析优化后的共享点精确度
            try:
                with open(output_file, 'r', encoding='utf-8') as f:
                    optimized_data = json.load(f)
                optimized_cells = optimized_data.get('cells', [])
                
                print("\n📊 优化后共享点精确度:")
                optimized_precision = analyze_shared_point_precision(optimized_cells)
                print(f"  共享点组数: {optimized_precision['total_shared_groups']}")
                print(f"  共享点总数: {optimized_precision['total_shared_points']}")
                print(f"  最大偏差: {optimized_precision['max_deviation']:.2f}px")
                print(f"  平均偏差: {optimized_precision['avg_deviation']:.2f}px")
                print(f"  精确组数: {optimized_precision['precise_groups']}/{optimized_precision['total_shared_groups']}")
                
                # 对比改进效果
                print("\n📈 精确度改进:")
                max_deviation_improvement = original_precision['max_deviation'] - optimized_precision['max_deviation']
                avg_deviation_improvement = original_precision['avg_deviation'] - optimized_precision['avg_deviation']
                precision_rate_improvement = (optimized_precision['precise_groups'] / max(1, optimized_precision['total_shared_groups'])) - \
                                            (original_precision['precise_groups'] / max(1, original_precision['total_shared_groups']))
                
                print(f"  最大偏差改进: {max_deviation_improvement:+.2f}px")
                print(f"  平均偏差改进: {avg_deviation_improvement:+.2f}px")
                print(f"  精确率改进: {precision_rate_improvement:+.1%}")
                
                if max_deviation_improvement > 0:
                    print("  ✅ 最大偏差显著降低")
                if avg_deviation_improvement > 0:
                    print("  ✅ 平均偏差显著降低")
                if precision_rate_improvement > 0:
                    print("  ✅ 精确组比例显著提升")
                
                # 详细分析精确度分布
                precise_count_before = original_precision['precise_groups']
                precise_count_after = optimized_precision['precise_groups']
                
                if precise_count_after > precise_count_before:
                    improvement = precise_count_after - precise_count_before
                    print(f"  ✅ 新增 {improvement} 个精确共享点组")
                
                results.append({
                    'sample': test_file.name,
                    'original_precision': original_precision,
                    'optimized_precision': optimized_precision,
                    'processing_time': processing_time
                })
                
            except Exception as e:
                print(f"  ⚠️  分析失败: {e}")
        else:
            print(f"  ❌ 处理失败: {result['error']}")
        
        # 清理测试文件
        try:
            if os.path.exists(output_file):
                os.remove(output_file)
        except:
            pass
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("📊 共享点对齐测试总结")
    print("=" * 60)
    
    if not results:
        print("❌ 没有成功的测试结果")
        return
    
    # 统计改进效果
    total_samples = len(results)
    
    avg_max_deviation_before = sum(r['original_precision']['max_deviation'] for r in results) / total_samples
    avg_max_deviation_after = sum(r['optimized_precision']['max_deviation'] for r in results) / total_samples
    
    avg_avg_deviation_before = sum(r['original_precision']['avg_deviation'] for r in results) / total_samples
    avg_avg_deviation_after = sum(r['optimized_precision']['avg_deviation'] for r in results) / total_samples
    
    total_precise_before = sum(r['original_precision']['precise_groups'] for r in results)
    total_precise_after = sum(r['optimized_precision']['precise_groups'] for r in results)
    
    total_groups_before = sum(r['original_precision']['total_shared_groups'] for r in results)
    total_groups_after = sum(r['optimized_precision']['total_shared_groups'] for r in results)
    
    print(f"成功测试样本数: {total_samples}")
    print(f"平均最大偏差改进: {avg_max_deviation_before:.2f}px -> {avg_max_deviation_after:.2f}px")
    print(f"平均偏差改进: {avg_avg_deviation_before:.2f}px -> {avg_avg_deviation_after:.2f}px")
    print(f"精确组数改进: {total_precise_before}/{total_groups_before} -> {total_precise_after}/{total_groups_after}")
    
    if avg_max_deviation_after < avg_max_deviation_before:
        improvement = avg_max_deviation_before - avg_max_deviation_after
        print(f"✅ 平均最大偏差减少了 {improvement:.2f}px")
    
    if avg_avg_deviation_after < avg_avg_deviation_before:
        improvement = avg_avg_deviation_before - avg_avg_deviation_after
        print(f"✅ 平均偏差减少了 {improvement:.2f}px")
    
    if total_precise_after > total_precise_before:
        improvement = total_precise_after - total_precise_before
        print(f"✅ 新增了 {improvement} 个精确共享点组")
    
    print(f"\n🎯 共享点对齐功能特点:")
    print("1. 精确的距离聚类算法")
    print("2. 加权中心计算")
    print("3. 保守的坐标更新策略")
    print("4. 强制网格一致性检查")
    print("5. 行列边界精确对齐")


def main():
    """主函数"""
    print("🧪 共享角点精确对齐功能测试")
    print("=" * 60)
    
    test_shared_point_alignment()
    
    print(f"\n🎉 测试完成！")
    print("共享点对齐专门解决:")
    print("- 相邻单元格角点坐标不一致")
    print("- 共享点精确度不够")
    print("- 网格边界对齐误差")
    print("- 表格结构一致性问题")
    
    return 0


if __name__ == "__main__":
    exit(main())
