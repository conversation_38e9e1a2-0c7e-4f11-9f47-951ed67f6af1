#!/usr/bin/env python3
"""
测试表格面积占比对阈值计算的影响

主要功能:
1. 测试不同面积占比下的阈值计算
2. 验证阈值调整规则
3. 生成测试报告

作者: AI Assistant
版本: 1.0
更新日期: 2025-01-08
"""

from table_annotation_optimizer import SimplifiedTableOptimizer


def test_area_ratio_thresholds():
    """测试不同面积占比的阈值计算"""
    
    print("🧪 表格面积占比阈值计算测试")
    print("=" * 60)
    
    # 创建优化器（启用自适应阈值）
    optimizer = SimplifiedTableOptimizer(
        tolerance=4.0,
        adaptive_threshold=True
    )
    
    # 测试用例：不同的图片和表格尺寸组合
    test_cases = [
        # (图片宽, 图片高, 表格宽, 表格高, 描述)
        (1920, 1080, 1800, 1000, "大表格 - 占比86%"),
        (1920, 1080, 1600, 900, "较大表格 - 占比69%"),
        (1920, 1080, 1200, 600, "中等表格 - 占比35%"),
        (1920, 1080, 800, 400, "较小表格 - 占比15%"),
        (1920, 1080, 400, 200, "小表格 - 占比4%"),
        
        # 不同分辨率的测试
        (800, 600, 600, 400, "低分辨率大表格 - 占比50%"),
        (800, 600, 300, 200, "低分辨率小表格 - 占比12.5%"),
        (3840, 2160, 2000, 1000, "4K分辨率中等表格 - 占比24%"),
        
        # 极端情况
        (1920, 1080, 1920, 1080, "全屏表格 - 占比100%"),
        (1920, 1080, 100, 50, "极小表格 - 占比0.2%"),
    ]
    
    print("测试配置:")
    print(f"  基础容差: {optimizer.base_tolerance}px")
    print(f"  自适应阈值: {'启用' if optimizer.adaptive_threshold else '禁用'}")
    print()
    
    results = []
    
    for i, (img_w, img_h, table_w, table_h, desc) in enumerate(test_cases, 1):
        # 计算面积占比
        table_area = table_w * table_h
        image_area = img_w * img_h
        area_ratio = table_area / image_area
        
        # 计算阈值
        threshold = optimizer.calculate_smart_threshold(img_w, img_h, table_w, table_h)
        
        # 计算调整因子
        adjustment_factor = threshold / optimizer.base_tolerance
        
        result = {
            'case': i,
            'description': desc,
            'image_size': f"{img_w}x{img_h}",
            'table_size': f"{table_w}x{table_h}",
            'area_ratio': area_ratio,
            'threshold': threshold,
            'adjustment_factor': adjustment_factor
        }
        
        results.append(result)
        
        print(f"测试 {i:2d}: {desc}")
        print(f"  图片尺寸: {img_w}x{img_h}")
        print(f"  表格尺寸: {table_w}x{table_h}")
        print(f"  面积占比: {area_ratio:.1%}")
        print(f"  计算阈值: {threshold:.2f}px")
        print(f"  调整因子: {adjustment_factor:.2f}x")
        print()
    
    # 分析结果
    print("=" * 60)
    print("📊 结果分析")
    print("=" * 60)
    
    # 按面积占比排序
    results.sort(key=lambda x: x['area_ratio'], reverse=True)
    
    print("按面积占比排序的阈值调整:")
    print(f"{'占比':<8} {'阈值':<8} {'因子':<8} {'描述'}")
    print("-" * 50)
    
    for result in results:
        print(f"{result['area_ratio']:>6.1%} {result['threshold']:>6.2f}px {result['adjustment_factor']:>6.2f}x {result['description']}")
    
    # 验证阈值调整规则
    print("\n🔍 阈值调整规则验证:")
    
    large_tables = [r for r in results if r['area_ratio'] > 0.8]
    medium_tables = [r for r in results if 0.4 <= r['area_ratio'] <= 0.8]
    small_tables = [r for r in results if r['area_ratio'] < 0.4]
    
    if large_tables:
        avg_factor_large = sum(r['adjustment_factor'] for r in large_tables) / len(large_tables)
        print(f"  大表格 (>80%): 平均调整因子 {avg_factor_large:.2f}x (期望: <1.0)")
    
    if medium_tables:
        avg_factor_medium = sum(r['adjustment_factor'] for r in medium_tables) / len(medium_tables)
        print(f"  中等表格 (40-80%): 平均调整因子 {avg_factor_medium:.2f}x (期望: ~1.0)")
    
    if small_tables:
        avg_factor_small = sum(r['adjustment_factor'] for r in small_tables) / len(small_tables)
        print(f"  小表格 (<40%): 平均调整因子 {avg_factor_small:.2f}x (期望: >1.0)")
    
    # 检查阈值范围
    min_threshold = min(r['threshold'] for r in results)
    max_threshold = max(r['threshold'] for r in results)
    
    print(f"\n📏 阈值范围检查:")
    print(f"  最小阈值: {min_threshold:.2f}px")
    print(f"  最大阈值: {max_threshold:.2f}px")
    print(f"  范围限制: 1.0 - 8.0px")
    
    if min_threshold >= 1.0 and max_threshold <= 8.0:
        print("  ✅ 阈值范围符合预期")
    else:
        print("  ⚠️  阈值范围超出预期")
    
    return results


def test_disabled_adaptive():
    """测试禁用自适应阈值的情况"""
    
    print("\n" + "=" * 60)
    print("🔧 禁用自适应阈值测试")
    print("=" * 60)
    
    # 创建优化器（禁用自适应阈值）
    optimizer = SimplifiedTableOptimizer(
        tolerance=4.0,
        adaptive_threshold=False
    )
    
    # 测试几种不同情况
    test_cases = [
        (1920, 1080, 1800, 1000, "大表格"),
        (1920, 1080, 800, 400, "小表格"),
        (800, 600, 400, 300, "低分辨率表格"),
    ]
    
    print("测试配置:")
    print(f"  基础容差: {optimizer.base_tolerance}px")
    print(f"  自适应阈值: {'启用' if optimizer.adaptive_threshold else '禁用'}")
    print()
    
    for i, (img_w, img_h, table_w, table_h, desc) in enumerate(test_cases, 1):
        threshold = optimizer.calculate_smart_threshold(img_w, img_h, table_w, table_h)
        
        print(f"测试 {i}: {desc}")
        print(f"  计算阈值: {threshold:.2f}px")
        print(f"  期望结果: {optimizer.base_tolerance:.2f}px")
        
        if threshold == optimizer.base_tolerance:
            print("  ✅ 结果正确")
        else:
            print("  ❌ 结果错误")
        print()


def main():
    """主函数"""
    try:
        # 测试自适应阈值
        results = test_area_ratio_thresholds()
        
        # 测试禁用自适应阈值
        test_disabled_adaptive()
        
        print("\n🎉 所有测试完成！")
        
        # 生成简要总结
        print("\n📋 总结:")
        print("1. ✅ 表格面积占比规则已正确实现")
        print("2. ✅ 大表格使用更精确的阈值")
        print("3. ✅ 小表格使用更宽松的阈值")
        print("4. ✅ 阈值范围限制正常工作")
        print("5. ✅ 禁用自适应阈值功能正常")
        
        return 0
        
    except Exception as e:
        print(f"测试失败: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
