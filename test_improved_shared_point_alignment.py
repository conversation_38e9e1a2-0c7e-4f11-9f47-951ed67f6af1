#!/usr/bin/env python3
"""
测试改进的共享点对齐算法
验证相邻单元格的角点是否能够精确共享同一个坐标
"""

import os
import json
import math
import numpy as np
from pathlib import Path
from table_annotation_optimizer import PerspectiveAwareOptimizer

def analyze_shared_point_precision(annotation_file: str) -> dict:
    """
    分析标注文件中共享点的精确度
    
    Args:
        annotation_file: 标注文件路径
        
    Returns:
        精确度分析结果
    """
    with open(annotation_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    cells = data['cells']
    
    # 收集所有角点
    all_points = []
    for cell_idx, cell in enumerate(cells):
        bbox = cell['bbox']
        for point_name in ['p1', 'p2', 'p3', 'p4']:
            point = bbox[point_name]
            all_points.append({
                'cell_idx': cell_idx,
                'point_name': point_name,
                'x': point[0],
                'y': point[1],
                'lloc': cell['lloc']
            })
    
    # 使用距离聚类找到共享点组
    shared_groups = []
    used_indices = set()
    cluster_threshold = 5.0  # 5像素内认为是潜在共享点
    
    for i, point1 in enumerate(all_points):
        if i in used_indices:
            continue
            
        group = [point1]
        used_indices.add(i)
        
        for j, point2 in enumerate(all_points):
            if j in used_indices:
                continue
                
            distance = math.sqrt((point1['x'] - point2['x'])**2 + 
                               (point1['y'] - point2['y'])**2)
            
            if distance <= cluster_threshold:
                group.append(point2)
                used_indices.add(j)
        
        if len(group) > 1:  # 只关心真正共享的点
            shared_groups.append(group)
    
    # 分析每个共享组的精确度
    precision_stats = {
        'total_shared_groups': len(shared_groups),
        'total_shared_points': sum(len(group) for group in shared_groups),
        'max_deviation': 0.0,
        'avg_deviation': 0.0,
        'precise_groups': 0,  # 偏差小于0.5像素的组
        'very_precise_groups': 0,  # 偏差小于0.1像素的组
        'perfect_groups': 0,  # 偏差为0的组
        'group_details': []
    }
    
    total_deviation = 0.0
    
    for group_idx, group in enumerate(shared_groups):
        if len(group) < 2:
            continue
            
        # 计算组内点的中心
        center_x = sum(p['x'] for p in group) / len(group)
        center_y = sum(p['y'] for p in group) / len(group)
        
        # 计算每个点到中心的距离
        distances = []
        for point in group:
            dist = math.sqrt((point['x'] - center_x)**2 + (point['y'] - center_y)**2)
            distances.append(dist)
        
        max_dist = max(distances)
        avg_dist = sum(distances) / len(distances)
        
        group_detail = {
            'group_id': group_idx,
            'point_count': len(group),
            'center': [center_x, center_y],
            'max_deviation': max_dist,
            'avg_deviation': avg_dist,
            'points': [(p['cell_idx'], p['point_name'], p['x'], p['y']) for p in group]
        }
        
        precision_stats['group_details'].append(group_detail)
        
        # 更新统计信息
        precision_stats['max_deviation'] = max(precision_stats['max_deviation'], max_dist)
        total_deviation += avg_dist
        
        if max_dist < 0.5:
            precision_stats['precise_groups'] += 1
        if max_dist < 0.1:
            precision_stats['very_precise_groups'] += 1
        if max_dist == 0.0:
            precision_stats['perfect_groups'] += 1
    
    if shared_groups:
        precision_stats['avg_deviation'] = total_deviation / len(shared_groups)
    
    return precision_stats

def test_improved_alignment():
    """测试改进的共享点对齐算法"""
    
    # 配置优化器
    optimizer = PerspectiveAwareOptimizer(
        tolerance=3.0,
        preserve_perspective=True,
        adaptive_threshold=False,
        quality_aware=False,
        angle_correction=False,
        conservative_mode=False  # 关闭保守模式以允许更大的调整
    )
    
    # 查找测试文件
    test_files = []
    
    # 从organized_dataset目录查找一些测试文件
    organized_dir = Path("organized_dataset")
    if organized_dir.exists():
        annotation_files = list(organized_dir.glob("*_table_annotation.json"))[:5]  # 取前5个文件测试
        test_files.extend(annotation_files)
    
    if not test_files:
        print("未找到测试文件，请确保organized_dataset目录存在并包含标注文件")
        return
    
    print(f"找到 {len(test_files)} 个测试文件")
    print("=" * 80)
    
    results = []
    
    for i, annotation_file in enumerate(test_files):
        print(f"\n测试文件 {i+1}/{len(test_files)}: {annotation_file.name}")
        print("-" * 60)
        
        try:
            # 分析原始文件的共享点精确度
            print("分析原始文件...")
            original_stats = analyze_shared_point_precision(str(annotation_file))
            
            # 查找对应的图片文件
            image_file = None
            base_name = annotation_file.stem.replace('_table_annotation', '')
            for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                img_path = annotation_file.parent / f"{base_name}{ext}"
                if img_path.exists():
                    image_file = str(img_path)
                    break
            
            # 执行优化
            print("执行优化...")
            result = optimizer.optimize_table_annotation(
                str(annotation_file),
                str(annotation_file).replace('.json', '_improved_aligned.json'),
                image_file
            )
            
            if result['success']:
                # 分析优化后文件的共享点精确度
                print("分析优化后文件...")
                optimized_stats = analyze_shared_point_precision(result['output_file'])
                
                # 计算改进效果
                improvement = {
                    'file': annotation_file.name,
                    'original': original_stats,
                    'optimized': optimized_stats,
                    'improvement': {
                        'max_deviation_reduction': original_stats['max_deviation'] - optimized_stats['max_deviation'],
                        'avg_deviation_reduction': original_stats['avg_deviation'] - optimized_stats['avg_deviation'],
                        'precise_groups_increase': optimized_stats['precise_groups'] - original_stats['precise_groups'],
                        'very_precise_groups_increase': optimized_stats['very_precise_groups'] - original_stats['very_precise_groups'],
                        'perfect_groups_increase': optimized_stats['perfect_groups'] - original_stats['perfect_groups']
                    }
                }
                
                results.append(improvement)
                
                # 打印结果
                print(f"原始文件:")
                print(f"  共享点组数: {original_stats['total_shared_groups']}")
                print(f"  最大偏差: {original_stats['max_deviation']:.3f} 像素")
                print(f"  平均偏差: {original_stats['avg_deviation']:.3f} 像素")
                print(f"  精确组数 (<0.5px): {original_stats['precise_groups']}")
                print(f"  超精确组数 (<0.1px): {original_stats['very_precise_groups']}")
                print(f"  完美组数 (=0px): {original_stats['perfect_groups']}")
                
                print(f"优化后文件:")
                print(f"  共享点组数: {optimized_stats['total_shared_groups']}")
                print(f"  最大偏差: {optimized_stats['max_deviation']:.3f} 像素")
                print(f"  平均偏差: {optimized_stats['avg_deviation']:.3f} 像素")
                print(f"  精确组数 (<0.5px): {optimized_stats['precise_groups']}")
                print(f"  超精确组数 (<0.1px): {optimized_stats['very_precise_groups']}")
                print(f"  完美组数 (=0px): {optimized_stats['perfect_groups']}")
                
                print(f"改进效果:")
                print(f"  最大偏差减少: {improvement['improvement']['max_deviation_reduction']:.3f} 像素")
                print(f"  平均偏差减少: {improvement['improvement']['avg_deviation_reduction']:.3f} 像素")
                print(f"  精确组增加: {improvement['improvement']['precise_groups_increase']}")
                print(f"  超精确组增加: {improvement['improvement']['very_precise_groups_increase']}")
                print(f"  完美组增加: {improvement['improvement']['perfect_groups_increase']}")
                
            else:
                print(f"优化失败: {result['error']}")
                
        except Exception as e:
            print(f"处理文件时出错: {e}")
    
    # 生成总结报告
    if results:
        print("\n" + "=" * 80)
        print("总结报告")
        print("=" * 80)
        
        total_files = len(results)
        total_max_reduction = sum(r['improvement']['max_deviation_reduction'] for r in results)
        total_avg_reduction = sum(r['improvement']['avg_deviation_reduction'] for r in results)
        total_precise_increase = sum(r['improvement']['precise_groups_increase'] for r in results)
        total_very_precise_increase = sum(r['improvement']['very_precise_groups_increase'] for r in results)
        total_perfect_increase = sum(r['improvement']['perfect_groups_increase'] for r in results)
        
        print(f"测试文件数: {total_files}")
        print(f"平均最大偏差减少: {total_max_reduction/total_files:.3f} 像素")
        print(f"平均平均偏差减少: {total_avg_reduction/total_files:.3f} 像素")
        print(f"总精确组增加: {total_precise_increase}")
        print(f"总超精确组增加: {total_very_precise_increase}")
        print(f"总完美组增加: {total_perfect_increase}")
        
        # 保存详细结果
        with open('improved_shared_point_alignment_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n详细结果已保存到: improved_shared_point_alignment_results.json")

if __name__ == "__main__":
    test_improved_alignment()
