#!/usr/bin/env python3
"""
简化高效的表格标注优化器

主要功能:
1. 智能角点对齐，减少表格线分叉
2. 自适应阈值，基于图像和表格特征
3. 保留所有原始属性不变
4. 高效批量处理

优化策略：
- 简化算法逻辑，专注核心问题
- 智能阈值计算，避免过度复杂
- 渐进式优化，确保稳定性

作者: AI Assistant
版本: 1.0 (简化优化版)
更新日期: 2025-01-08
"""

import os
import json
import time
import math
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
import argparse
from typing import List, Dict, Tuple, Optional, Any
from PIL import Image


class SimplifiedTableOptimizer:
    """简化高效的表格标注优化器"""

    def __init__(self, tolerance: float = 3.0, adaptive_threshold: bool = True,
                 merge_threshold_factor: float = 1.5, alignment_strength: float = 0.8,
                 preserve_perspective: bool = True):
        """
        初始化优化器

        Args:
            tolerance: 基础容差阈值（像素）
            adaptive_threshold: 是否使用自适应阈值
            merge_threshold_factor: 合并阈值因子
            alignment_strength: 对齐强度 (0.0-1.0)
            preserve_perspective: 是否保持透视变换
        """
        self.base_tolerance = tolerance
        self.adaptive_threshold = adaptive_threshold
        self.merge_threshold_factor = merge_threshold_factor
        self.alignment_strength = alignment_strength
        self.preserve_perspective = preserve_perspective
        
        # 运行时变量
        self.tolerance = tolerance
        self.image_info = None
        self.cells = []
        self.table_ind = ""
        self.image_path = ""
        self.original_data = None

    def get_image_info(self, image_path: str) -> Optional[Dict[str, int]]:
        """
        获取图片信息

        Args:
            image_path: 图片文件路径

        Returns:
            包含图片宽度和高度的字典，失败时返回None
        """
        try:
            if image_path and Path(image_path).exists():
                with Image.open(image_path) as img:
                    return {'width': img.size[0], 'height': img.size[1]}
        except Exception as e:
            print(f"无法读取图片 {image_path}: {e}")
        return None

    def calculate_smart_threshold(self, img_width: int, img_height: int,
                                table_width: float, table_height: float) -> float:
        """
        智能计算自适应阈值

        Args:
            img_width: 图片宽度
            img_height: 图片高度
            table_width: 表格宽度
            table_height: 表格高度

        Returns:
            计算得到的智能阈值
        """
        if not self.adaptive_threshold:
            return self.base_tolerance

        # 1. 基于分辨率的基础因子
        total_pixels = img_width * img_height
        resolution_factor = math.sqrt(total_pixels / (1920 * 1080))  # 以1080p为基准
        resolution_factor = max(0.5, min(2.0, resolution_factor))

        # 2. 基于表格占比的尺寸因子
        if table_width > 0 and table_height > 0:
            table_area = table_width * table_height
            image_area = img_width * img_height
            area_ratio = table_area / image_area
            
            # 简化的尺寸因子计算
            if area_ratio > 0.8:
                size_factor = 0.7  # 大表格需要更精确
            elif area_ratio > 0.5:
                size_factor = 1.0  # 中等表格
            elif area_ratio > 0.2:
                size_factor = 1.3  # 小表格可以宽松一些
            else:
                size_factor = 1.5  # 很小的表格
        else:
            size_factor = 1.0

        # 3. 综合计算
        adaptive_threshold = self.base_tolerance * (0.6 * resolution_factor + 0.4 * size_factor)
        
        # 限制范围
        adaptive_threshold = max(1.0, min(10.0, adaptive_threshold))
        
        return adaptive_threshold

    def calculate_table_bounds(self, cells: List[Dict]) -> Tuple[float, float]:
        """
        计算表格边界尺寸

        Args:
            cells: 单元格列表

        Returns:
            表格宽度和高度的元组
        """
        if not cells:
            return 0.0, 0.0

        all_x = []
        all_y = []

        for cell in cells:
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                if point_name in bbox:
                    point = bbox[point_name]
                    all_x.append(point[0])
                    all_y.append(point[1])

        if not all_x or not all_y:
            return 0.0, 0.0

        width = max(all_x) - min(all_x)
        height = max(all_y) - min(all_y)

        return width, height

    def load_annotation(self, json_path: str, image_path: Optional[str] = None):
        """
        加载表格标注文件

        Args:
            json_path: 标注文件路径
            image_path: 对应的图片文件路径（用于自适应阈值计算）
        """
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 保存原始数据以便保护所有属性
        self.original_data = data.copy()

        self.cells = data['cells']
        self.table_ind = data.get('table_ind', '')
        self.image_path = data.get('image_path', '')

        # 获取图片信息用于自适应阈值计算
        if image_path:
            self.image_info = self.get_image_info(image_path)

            # 计算表格边界用于自适应阈值
            if self.image_info:
                table_width, table_height = self.calculate_table_bounds(self.cells)
                self.tolerance = self.calculate_smart_threshold(
                    self.image_info['width'],
                    self.image_info['height'],
                    table_width,
                    table_height
                )

                print(f"智能阈值计算结果: {self.tolerance:.2f}px")
                print(f"  图片尺寸: {self.image_info['width']}x{self.image_info['height']}")
                print(f"  表格尺寸: {table_width:.1f}x{table_height:.1f}")
                if table_width > 0 and table_height > 0:
                    area_ratio = (table_width * table_height) / (self.image_info['width'] * self.image_info['height'])
                    print(f"  表格占比: {area_ratio:.1%}")
            else:
                print(f"无法获取图片信息，使用基础阈值: {self.base_tolerance}")
                self.tolerance = self.base_tolerance

        print(f"加载了 {len(self.cells)} 个单元格")

    def find_nearby_points(self) -> List[List[Dict]]:
        """
        查找相近的角点组

        Returns:
            相近角点组列表
        """
        all_points = []

        # 收集所有角点
        for cell_idx, cell in enumerate(self.cells):
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                if point_name in bbox:
                    point = bbox[point_name]
                    all_points.append({
                        'coords': point,
                        'cell_idx': cell_idx,
                        'point_name': point_name,
                        'x': point[0],
                        'y': point[1]
                    })

        # 使用合并阈值查找相近点组
        merge_threshold = self.tolerance * self.merge_threshold_factor
        point_groups = []
        used_indices = set()

        for i, point1 in enumerate(all_points):
            if i in used_indices:
                continue

            group = [point1]
            used_indices.add(i)

            for j, point2 in enumerate(all_points):
                if j in used_indices:
                    continue

                # 计算欧几里得距离
                distance = math.sqrt(
                    (point1['x'] - point2['x'])**2 +
                    (point1['y'] - point2['y'])**2
                )

                if distance <= merge_threshold:
                    group.append(point2)
                    used_indices.add(j)

            if len(group) > 1:
                point_groups.append(group)

        return point_groups

    def align_nearby_points(self):
        """
        对齐相近的角点
        """
        point_groups = self.find_nearby_points()
        
        if not point_groups:
            print("  未发现需要对齐的相近角点")
            return

        print(f"  发现 {len(point_groups)} 组相近角点")
        
        aligned_count = 0
        for group in point_groups:
            if len(group) < 2:
                continue

            # 计算组的加权中心
            center_x = sum(p['x'] for p in group) / len(group)
            center_y = sum(p['y'] for p in group) / len(group)

            # 应用对齐强度
            for point in group:
                original_x = point['x']
                original_y = point['y']
                
                # 计算新坐标（应用对齐强度）
                new_x = original_x + (center_x - original_x) * self.alignment_strength
                new_y = original_y + (center_y - original_y) * self.alignment_strength
                
                # 更新坐标
                self.cells[point['cell_idx']]['bbox'][point['point_name']] = [new_x, new_y]
                aligned_count += 1

        if aligned_count > 0:
            print(f"  对齐了 {aligned_count} 个角点")

    def optimize_table_annotation(self, input_file: str, output_file: str, 
                                image_file: Optional[str] = None) -> Dict[str, Any]:
        """
        优化表格标注

        Args:
            input_file: 输入标注文件路径
            output_file: 输出标注文件路径
            image_file: 对应的图片文件路径

        Returns:
            处理结果字典
        """
        try:
            print(f"开始优化: {Path(input_file).name}")
            
            # 加载标注数据
            self.load_annotation(input_file, image_file)
            
            # 执行优化
            print("执行角点对齐优化...")
            self.align_nearby_points()
            
            # 保存结果
            self.save_annotation(output_file)
            
            result = {
                'success': True,
                'cell_count': len(self.cells),
                'adaptive_threshold': self.tolerance if self.adaptive_threshold else None,
                'image_info': self.image_info
            }
            
            print(f"优化完成: {Path(output_file).name}")
            return result
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'cell_count': 0
            }

    def save_annotation(self, output_file: str):
        """
        保存优化后的标注文件，保护所有原始属性

        Args:
            output_file: 输出文件路径
        """
        # 使用原始数据作为基础，只更新cells
        output_data = self.original_data.copy()
        output_data['cells'] = self.cells
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)


def main():
    """命令行主函数"""
    parser = argparse.ArgumentParser(description='简化表格标注优化器')
    parser.add_argument('input_file', help='输入标注文件路径')
    parser.add_argument('-o', '--output', help='输出文件路径')
    parser.add_argument('-i', '--image', help='对应的图片文件路径')
    parser.add_argument('-t', '--tolerance', type=float, default=3.0, help='基础容差阈值（像素）')
    parser.add_argument('--no-adaptive', action='store_true', help='禁用自适应阈值')
    parser.add_argument('--merge-factor', type=float, default=1.5, help='合并阈值因子')
    parser.add_argument('--alignment-strength', type=float, default=0.8, help='对齐强度 (0.0-1.0)')

    args = parser.parse_args()

    # 确定输出文件路径
    if args.output:
        output_path = args.output
    else:
        input_path = Path(args.input_file)
        output_path = input_path.parent / f"{input_path.stem}_optimized{input_path.suffix}"

    # 创建优化器
    optimizer = SimplifiedTableOptimizer(
        tolerance=args.tolerance,
        adaptive_threshold=not args.no_adaptive,
        merge_threshold_factor=args.merge_factor,
        alignment_strength=args.alignment_strength
    )

    try:
        # 执行优化
        result = optimizer.optimize_table_annotation(
            args.input_file,
            output_path,
            args.image
        )

        if result['success']:
            print(f"\n优化完成！")
            print(f"输入文件: {args.input_file}")
            print(f"输出文件: {output_path}")
            print(f"单元格数: {result['cell_count']}")
            if result['adaptive_threshold']:
                print(f"自适应阈值: {result['adaptive_threshold']:.2f} 像素")
                if result['image_info']:
                    print(f"图片尺寸: {result['image_info']['width']}x{result['image_info']['height']}")
            else:
                print(f"固定阈值: {args.tolerance} 像素")
        else:
            print(f"优化失败: {result['error']}")
            return 1
        
    except Exception as e:
        print(f"错误: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
