#!/usr/bin/env python3
"""
可视化共享点对齐改进效果
"""

import json
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path

def load_test_results():
    """加载测试结果"""
    results_file = Path("improved_shared_point_alignment_results.json")
    if results_file.exists():
        with open(results_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return []

def create_improvement_visualization():
    """创建改进效果可视化图表"""
    results = load_test_results()
    
    if not results:
        print("未找到测试结果文件")
        return
    
    # 准备数据
    file_names = [r['file'].replace('_table_annotation.json', '') for r in results]
    
    # 偏差数据
    original_max_dev = [r['original']['max_deviation'] for r in results]
    optimized_max_dev = [r['optimized']['max_deviation'] for r in results]
    original_avg_dev = [r['original']['avg_deviation'] for r in results]
    optimized_avg_dev = [r['optimized']['avg_deviation'] for r in results]
    
    # 精确组数据
    original_precise = [r['original']['precise_groups'] for r in results]
    optimized_precise = [r['optimized']['precise_groups'] for r in results]
    original_very_precise = [r['original']['very_precise_groups'] for r in results]
    optimized_very_precise = [r['optimized']['very_precise_groups'] for r in results]
    original_perfect = [r['original']['perfect_groups'] for r in results]
    optimized_perfect = [r['optimized']['perfect_groups'] for r in results]
    
    # 创建图表
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('共享点对齐改进效果分析', fontsize=16, fontweight='bold')
    
    # 1. 最大偏差对比
    x = np.arange(len(file_names))
    width = 0.35
    
    axes[0, 0].bar(x - width/2, original_max_dev, width, label='优化前', alpha=0.8, color='red')
    axes[0, 0].bar(x + width/2, optimized_max_dev, width, label='优化后', alpha=0.8, color='green')
    axes[0, 0].set_title('最大偏差对比 (像素)')
    axes[0, 0].set_ylabel('偏差 (像素)')
    axes[0, 0].set_xticks(x)
    axes[0, 0].set_xticklabels(file_names, rotation=45, ha='right')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 平均偏差对比
    axes[0, 1].bar(x - width/2, original_avg_dev, width, label='优化前', alpha=0.8, color='red')
    axes[0, 1].bar(x + width/2, optimized_avg_dev, width, label='优化后', alpha=0.8, color='green')
    axes[0, 1].set_title('平均偏差对比 (像素)')
    axes[0, 1].set_ylabel('偏差 (像素)')
    axes[0, 1].set_xticks(x)
    axes[0, 1].set_xticklabels(file_names, rotation=45, ha='right')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 精确组数对比 (<0.5px)
    axes[0, 2].bar(x - width/2, original_precise, width, label='优化前', alpha=0.8, color='red')
    axes[0, 2].bar(x + width/2, optimized_precise, width, label='优化后', alpha=0.8, color='green')
    axes[0, 2].set_title('精确组数对比 (<0.5px)')
    axes[0, 2].set_ylabel('组数')
    axes[0, 2].set_xticks(x)
    axes[0, 2].set_xticklabels(file_names, rotation=45, ha='right')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)
    
    # 4. 超精确组数对比 (<0.1px)
    axes[1, 0].bar(x - width/2, original_very_precise, width, label='优化前', alpha=0.8, color='red')
    axes[1, 0].bar(x + width/2, optimized_very_precise, width, label='优化后', alpha=0.8, color='green')
    axes[1, 0].set_title('超精确组数对比 (<0.1px)')
    axes[1, 0].set_ylabel('组数')
    axes[1, 0].set_xticks(x)
    axes[1, 0].set_xticklabels(file_names, rotation=45, ha='right')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 5. 完美组数对比 (=0px)
    axes[1, 1].bar(x - width/2, original_perfect, width, label='优化前', alpha=0.8, color='red')
    axes[1, 1].bar(x + width/2, optimized_perfect, width, label='优化后', alpha=0.8, color='green')
    axes[1, 1].set_title('完美组数对比 (=0px)')
    axes[1, 1].set_ylabel('组数')
    axes[1, 1].set_xticks(x)
    axes[1, 1].set_xticklabels(file_names, rotation=45, ha='right')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    # 6. 改进效果总结
    max_dev_reduction = [r['improvement']['max_deviation_reduction'] for r in results]
    avg_dev_reduction = [r['improvement']['avg_deviation_reduction'] for r in results]
    precise_increase = [r['improvement']['precise_groups_increase'] for r in results]
    very_precise_increase = [r['improvement']['very_precise_groups_increase'] for r in results]
    perfect_increase = [r['improvement']['perfect_groups_increase'] for r in results]
    
    # 创建改进效果雷达图
    categories = ['最大偏差\n减少', '平均偏差\n减少', '精确组\n增加', '超精确组\n增加', '完美组\n增加']
    
    # 标准化数据到0-1范围
    max_values = [
        max(max_dev_reduction) if max(max_dev_reduction) > 0 else 1,
        max(avg_dev_reduction) if max(avg_dev_reduction) > 0 else 1,
        max(precise_increase) if max(precise_increase) > 0 else 1,
        max(very_precise_increase) if max(very_precise_increase) > 0 else 1,
        max(perfect_increase) if max(perfect_increase) > 0 else 1
    ]
    
    # 计算平均改进效果
    avg_improvements = [
        np.mean(max_dev_reduction) / max_values[0] if max_values[0] > 0 else 0,
        np.mean(avg_dev_reduction) / max_values[1] if max_values[1] > 0 else 0,
        np.mean(precise_increase) / max_values[2] if max_values[2] > 0 else 0,
        np.mean(very_precise_increase) / max_values[3] if max_values[3] > 0 else 0,
        np.mean(perfect_increase) / max_values[4] if max_values[4] > 0 else 0
    ]
    
    # 绘制改进效果条形图
    axes[1, 2].bar(range(len(categories)), avg_improvements, color=['skyblue', 'lightgreen', 'gold', 'orange', 'lightcoral'])
    axes[1, 2].set_title('平均改进效果 (标准化)')
    axes[1, 2].set_ylabel('改进程度')
    axes[1, 2].set_xticks(range(len(categories)))
    axes[1, 2].set_xticklabels(categories, rotation=45, ha='right')
    axes[1, 2].set_ylim(0, 1)
    axes[1, 2].grid(True, alpha=0.3)
    
    # 在条形图上添加数值标签
    for i, v in enumerate(avg_improvements):
        axes[1, 2].text(i, v + 0.01, f'{v:.2f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('shared_point_alignment_improvement.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 打印统计摘要
    print("\n" + "="*60)
    print("共享点对齐改进效果统计摘要")
    print("="*60)
    
    total_files = len(results)
    print(f"测试文件数量: {total_files}")
    print(f"平均最大偏差减少: {np.mean(max_dev_reduction):.3f} 像素")
    print(f"平均平均偏差减少: {np.mean(avg_dev_reduction):.3f} 像素")
    print(f"总精确组增加: {sum(precise_increase)}")
    print(f"总超精确组增加: {sum(very_precise_increase)}")
    print(f"总完美组增加: {sum(perfect_increase)}")
    
    # 计算改进率
    total_original_precise = sum(r['original']['precise_groups'] for r in results)
    total_optimized_precise = sum(r['optimized']['precise_groups'] for r in results)
    precise_improvement_rate = (total_optimized_precise - total_original_precise) / total_original_precise * 100 if total_original_precise > 0 else 0
    
    total_original_perfect = sum(r['original']['perfect_groups'] for r in results)
    total_optimized_perfect = sum(r['optimized']['perfect_groups'] for r in results)
    perfect_improvement_rate = (total_optimized_perfect - total_original_perfect) / total_original_perfect * 100 if total_original_perfect > 0 else 0
    
    print(f"\n改进率:")
    print(f"精确组改进率: {precise_improvement_rate:.1f}%")
    print(f"完美组改进率: {perfect_improvement_rate:.1f}%")
    
    # 成功率统计
    successful_files = sum(1 for r in results if r['improvement']['max_deviation_reduction'] > 0)
    success_rate = successful_files / total_files * 100
    print(f"成功改进文件比例: {success_rate:.1f}% ({successful_files}/{total_files})")

def create_precision_distribution():
    """创建精确度分布图"""
    results = load_test_results()
    
    if not results:
        return
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 收集所有组的精确度数据
    original_deviations = []
    optimized_deviations = []
    
    for result in results:
        for group in result['original']['group_details']:
            original_deviations.append(group['max_deviation'])
        for group in result['optimized']['group_details']:
            optimized_deviations.append(group['max_deviation'])
    
    # 绘制精确度分布直方图
    bins = np.linspace(0, max(max(original_deviations), max(optimized_deviations)), 20)
    
    ax1.hist(original_deviations, bins=bins, alpha=0.7, label='优化前', color='red', density=True)
    ax1.hist(optimized_deviations, bins=bins, alpha=0.7, label='优化后', color='green', density=True)
    ax1.set_title('共享点精确度分布')
    ax1.set_xlabel('最大偏差 (像素)')
    ax1.set_ylabel('密度')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 绘制累积分布
    original_sorted = np.sort(original_deviations)
    optimized_sorted = np.sort(optimized_deviations)
    
    original_cumulative = np.arange(1, len(original_sorted) + 1) / len(original_sorted)
    optimized_cumulative = np.arange(1, len(optimized_sorted) + 1) / len(optimized_sorted)
    
    ax2.plot(original_sorted, original_cumulative, label='优化前', color='red', linewidth=2)
    ax2.plot(optimized_sorted, optimized_cumulative, label='优化后', color='green', linewidth=2)
    ax2.set_title('共享点精确度累积分布')
    ax2.set_xlabel('最大偏差 (像素)')
    ax2.set_ylabel('累积概率')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 添加精确度阈值线
    ax2.axvline(x=0.5, color='orange', linestyle='--', alpha=0.7, label='精确阈值 (0.5px)')
    ax2.axvline(x=0.1, color='purple', linestyle='--', alpha=0.7, label='超精确阈值 (0.1px)')
    ax2.legend()
    
    plt.tight_layout()
    plt.savefig('shared_point_precision_distribution.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    print("创建共享点对齐改进效果可视化...")
    create_improvement_visualization()
    
    print("\n创建精确度分布图...")
    create_precision_distribution()
    
    print("\n可视化图表已保存:")
    print("- shared_point_alignment_improvement.png")
    print("- shared_point_precision_distribution.png")
