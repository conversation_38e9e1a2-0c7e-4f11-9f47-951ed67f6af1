#!/usr/bin/env python3
"""
测试当前表格标注优化器的性能和效果

主要功能:
1. 测试当前算法的处理效果
2. 分析性能瓶颈
3. 识别优化机会
4. 生成详细的测试报告

作者: AI Assistant
版本: 1.0
更新日期: 2025-01-08
"""

import os
import json
import time
import math
import traceback
from pathlib import Path
from typing import List, Dict, Any, Tuple

# 导入当前的优化器
try:
    from table_annotation_optimizer import SimplifiedTableOptimizer
except ImportError:
    try:
        from simplified_table_optimizer import SimplifiedTableOptimizer
    except ImportError:
        print("❌ 无法导入优化器类，请检查文件是否存在")
        exit(1)


def calculate_alignment_metrics(cells: List[Dict]) -> Dict[str, float]:
    """
    计算对齐质量指标

    Args:
        cells: 单元格列表

    Returns:
        质量指标字典
    """
    if not cells:
        return {
            'horizontal_variance': 0,
            'vertical_variance': 0,
            'point_count': 0,
            'alignment_score': 0
        }

    # 收集所有角点
    all_points = []
    for cell in cells:
        bbox = cell.get('bbox', {})
        for point_name in ['p1', 'p2', 'p3', 'p4']:
            if point_name in bbox:
                point = bbox[point_name]
                all_points.append({
                    'x': point[0],
                    'y': point[1],
                    'point_name': point_name
                })

    if len(all_points) < 2:
        return {
            'horizontal_variance': 0,
            'vertical_variance': 0,
            'point_count': len(all_points),
            'alignment_score': 100
        }

    # 计算水平和垂直方向的方差
    x_coords = [p['x'] for p in all_points]
    y_coords = [p['y'] for p in all_points]
    
    # 计算相近点的方差（用于评估对齐质量）
    horizontal_variances = []
    vertical_variances = []
    tolerance = 10.0  # 10像素容差认为是应该对齐的点

    # 水平对齐方差
    for i, point1 in enumerate(all_points):
        nearby_y = [point2['y'] for j, point2 in enumerate(all_points) 
                   if i != j and abs(point1['y'] - point2['y']) <= tolerance]
        if len(nearby_y) > 0:
            nearby_y.append(point1['y'])
            variance = sum((y - sum(nearby_y)/len(nearby_y))**2 for y in nearby_y) / len(nearby_y)
            horizontal_variances.append(variance)

    # 垂直对齐方差
    for i, point1 in enumerate(all_points):
        nearby_x = [point2['x'] for j, point2 in enumerate(all_points) 
                   if i != j and abs(point1['x'] - point2['x']) <= tolerance]
        if len(nearby_x) > 0:
            nearby_x.append(point1['x'])
            variance = sum((x - sum(nearby_x)/len(nearby_x))**2 for x in nearby_x) / len(nearby_x)
            vertical_variances.append(variance)

    avg_horizontal_variance = sum(horizontal_variances) / len(horizontal_variances) if horizontal_variances else 0
    avg_vertical_variance = sum(vertical_variances) / len(vertical_variances) if vertical_variances else 0

    # 计算综合对齐分数（方差越小，分数越高）
    max_variance = 100.0  # 假设最大方差为100
    horizontal_score = max(0, 100 - (avg_horizontal_variance / max_variance) * 100)
    vertical_score = max(0, 100 - (avg_vertical_variance / max_variance) * 100)
    alignment_score = (horizontal_score + vertical_score) / 2

    return {
        'horizontal_variance': avg_horizontal_variance,
        'vertical_variance': avg_vertical_variance,
        'point_count': len(all_points),
        'alignment_score': alignment_score,
        'horizontal_score': horizontal_score,
        'vertical_score': vertical_score
    }


def test_single_file(annotation_file: Path, config: Dict[str, Any]) -> Dict[str, Any]:
    """
    测试单个文件

    Args:
        annotation_file: 标注文件路径
        config: 配置参数

    Returns:
        测试结果
    """
    print(f"\n📁 测试文件: {annotation_file.name}")
    print(f"⚙️  配置: {config}")
    
    try:
        # 查找对应图片
        image_file = None
        for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
            img_path = annotation_file.parent / f"{annotation_file.stem.replace('_table_annotation', '')}{ext}"
            if img_path.exists():
                image_file = str(img_path)
                break
        
        if image_file:
            print(f"🖼️  找到图片: {Path(image_file).name}")
        else:
            print("⚠️  未找到对应图片文件")
        
        # 加载原始数据计算质量
        with open(annotation_file, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        
        original_metrics = calculate_alignment_metrics(original_data['cells'])
        print(f"📊 原始质量: 对齐分数={original_metrics['alignment_score']:.1f}, 点数={original_metrics['point_count']}")
        
        # 创建优化器
        optimizer = SimplifiedTableOptimizer(
            tolerance=config['tolerance'],
            adaptive_threshold=config['adaptive_threshold'],
            merge_threshold_factor=config['merge_threshold_factor'],
            alignment_strength=config['alignment_strength']
        )
        
        # 执行优化
        output_file = f"test_output_{annotation_file.name}"
        start_time = time.time()
        
        result = optimizer.optimize_table_annotation(
            str(annotation_file),
            output_file,
            image_file
        )
        
        processing_time = time.time() - start_time
        
        if not result['success']:
            print(f"❌ 处理失败: {result['error']}")
            return {
                'success': False,
                'error': result['error'],
                'processing_time': processing_time,
                'file': annotation_file.name
            }
        
        # 加载优化后的数据计算质量
        with open(output_file, 'r', encoding='utf-8') as f:
            optimized_data = json.load(f)
        
        optimized_metrics = calculate_alignment_metrics(optimized_data['cells'])
        
        # 计算改进效果
        score_improvement = optimized_metrics['alignment_score'] - original_metrics['alignment_score']
        horizontal_improvement = optimized_metrics['horizontal_score'] - original_metrics['horizontal_score']
        vertical_improvement = optimized_metrics['vertical_score'] - original_metrics['vertical_score']
        
        # 清理测试文件
        if os.path.exists(output_file):
            os.remove(output_file)
        
        test_result = {
            'success': True,
            'file': annotation_file.name,
            'processing_time': processing_time,
            'cell_count': result['cell_count'],
            'adaptive_threshold': result.get('adaptive_threshold'),
            'original_metrics': original_metrics,
            'optimized_metrics': optimized_metrics,
            'score_improvement': score_improvement,
            'horizontal_improvement': horizontal_improvement,
            'vertical_improvement': vertical_improvement,
            'config': config
        }
        
        print(f"✅ 处理成功")
        print(f"📊 单元格数: {result['cell_count']}")
        print(f"⏱️  处理时间: {processing_time:.3f}s")
        if result.get('adaptive_threshold'):
            print(f"🎯 自适应阈值: {result['adaptive_threshold']:.2f}px")
        print(f"📈 对齐分数改进: {score_improvement:+.1f}")
        print(f"📈 水平改进: {horizontal_improvement:+.1f}")
        print(f"📈 垂直改进: {vertical_improvement:+.1f}")
        
        return test_result
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        print(f"🔍 详细错误: {traceback.format_exc()}")
        return {
            'success': False,
            'error': str(e),
            'processing_time': 0,
            'file': annotation_file.name
        }


def run_comprehensive_test():
    """
    运行综合测试
    """
    print("🚀 表格标注优化器综合测试")
    print("=" * 60)
    
    # 查找测试文件
    test_files = []
    
    # 查找当前目录下的标注文件
    current_dir = Path('.')
    annotation_files = list(current_dir.glob('*_table_annotation.json'))
    
    if annotation_files:
        test_files.extend(annotation_files[:5])  # 最多测试5个文件
    
    # 如果当前目录没有，查找子目录
    if not test_files:
        for subdir in current_dir.iterdir():
            if subdir.is_dir() and not subdir.name.startswith('.'):
                annotation_files = list(subdir.glob('*_table_annotation.json'))
                if annotation_files:
                    test_files.extend(annotation_files[:5])
                    break
    
    if not test_files:
        print("❌ 未找到测试文件")
        print("💡 请确保当前目录或子目录中有 *_table_annotation.json 文件")
        return
    
    print(f"📁 找到 {len(test_files)} 个测试文件")
    for f in test_files:
        print(f"   - {f.name}")
    
    # 测试配置
    configs = [
        {
            'name': '当前默认配置',
            'tolerance': 3.0,
            'adaptive_threshold': True,
            'merge_threshold_factor': 1.5,
            'alignment_strength': 0.8
        },
        {
            'name': '高精度配置',
            'tolerance': 2.0,
            'adaptive_threshold': True,
            'merge_threshold_factor': 1.2,
            'alignment_strength': 0.9
        },
        {
            'name': '快速配置',
            'tolerance': 4.0,
            'adaptive_threshold': False,
            'merge_threshold_factor': 2.0,
            'alignment_strength': 0.7
        },
        {
            'name': '保守配置',
            'tolerance': 2.5,
            'adaptive_threshold': True,
            'merge_threshold_factor': 1.3,
            'alignment_strength': 0.6
        }
    ]
    
    # 运行测试
    all_results = []
    
    for config in configs:
        print(f"\n🔧 测试配置: {config['name']}")
        print("-" * 40)
        
        config_results = []
        
        for test_file in test_files:
            result = test_single_file(test_file, config)
            if result['success']:
                config_results.append(result)
        
        if config_results:
            # 计算平均结果
            avg_score_improvement = sum(r['score_improvement'] for r in config_results) / len(config_results)
            avg_horizontal_improvement = sum(r['horizontal_improvement'] for r in config_results) / len(config_results)
            avg_vertical_improvement = sum(r['vertical_improvement'] for r in config_results) / len(config_results)
            avg_processing_time = sum(r['processing_time'] for r in config_results) / len(config_results)
            
            summary = {
                'config': config,
                'test_count': len(config_results),
                'avg_score_improvement': avg_score_improvement,
                'avg_horizontal_improvement': avg_horizontal_improvement,
                'avg_vertical_improvement': avg_vertical_improvement,
                'avg_processing_time': avg_processing_time,
                'success_rate': len(config_results) / len(test_files) * 100,
                'results': config_results
            }
            
            all_results.append(summary)
            
            print(f"\n📊 {config['name']} 平均结果:")
            print(f"  成功率: {summary['success_rate']:.1f}%")
            print(f"  平均对齐改进: {avg_score_improvement:+.1f}")
            print(f"  平均水平改进: {avg_horizontal_improvement:+.1f}")
            print(f"  平均垂直改进: {avg_vertical_improvement:+.1f}")
            print(f"  平均处理时间: {avg_processing_time:.3f}s")
    
    # 生成最终报告
    print("\n" + "=" * 60)
    print("🏆 综合测试报告")
    print("=" * 60)
    
    if all_results:
        # 按综合改进效果排序
        all_results.sort(key=lambda x: x['avg_score_improvement'], reverse=True)
        
        print("📈 配置排名（按平均对齐改进）:")
        for i, summary in enumerate(all_results, 1):
            print(f"{i}. {summary['config']['name']}: {summary['avg_score_improvement']:+.1f} 分")
            print(f"   成功率: {summary['success_rate']:.1f}%, 处理时间: {summary['avg_processing_time']:.3f}s")
        
        # 分析最佳配置
        best_config = all_results[0]
        print(f"\n🎯 最佳配置: {best_config['config']['name']}")
        print(f"  参数: {best_config['config']}")
        print(f"  平均改进: {best_config['avg_score_improvement']:+.1f} 分")
        print(f"  成功率: {best_config['success_rate']:.1f}%")
        print(f"  平均处理时间: {best_config['avg_processing_time']:.3f}s")
        
        # 性能分析
        print(f"\n⚡ 性能分析:")
        fastest_config = min(all_results, key=lambda x: x['avg_processing_time'])
        print(f"  最快配置: {fastest_config['config']['name']} ({fastest_config['avg_processing_time']:.3f}s)")
        
        most_reliable = max(all_results, key=lambda x: x['success_rate'])
        print(f"  最可靠配置: {most_reliable['config']['name']} ({most_reliable['success_rate']:.1f}%)")
        
        # 保存详细报告
        report_file = "current_optimizer_test_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        # 优化建议
        print(f"\n💡 优化建议:")
        if best_config['avg_score_improvement'] < 5:
            print("  - 当前算法改进效果有限，建议调整核心算法逻辑")
        if best_config['avg_processing_time'] > 0.1:
            print("  - 处理速度较慢，建议优化算法效率")
        if best_config['success_rate'] < 100:
            print("  - 存在处理失败的情况，建议增强错误处理")
            
    else:
        print("❌ 没有成功的测试结果")
        print("💡 建议检查:")
        print("  - 输入文件格式是否正确")
        print("  - 算法参数是否合理")
        print("  - 是否存在代码错误")


def main():
    """主函数"""
    try:
        run_comprehensive_test()
    except Exception as e:
        print(f"测试失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
