#!/usr/bin/env python3
"""
简化优化器测试脚本

主要功能:
1. 测试简化优化器的效果
2. 对比不同参数设置
3. 生成测试报告

作者: AI Assistant
版本: 1.0
更新日期: 2025-01-08
"""

import os
import json
import time
import math
from pathlib import Path
from typing import List, Dict, Any, Tuple

from simplified_table_optimizer import SimplifiedTableOptimizer


def calculate_alignment_quality(cells: List[Dict]) -> Dict[str, float]:
    """
    计算对齐质量指标

    Args:
        cells: 单元格列表

    Returns:
        质量指标字典
    """
    if not cells:
        return {'horizontal_error': 0, 'vertical_error': 0, 'total_points': 0}

    # 收集所有角点
    all_points = []
    for cell in cells:
        bbox = cell['bbox']
        for point_name in ['p1', 'p2', 'p3', 'p4']:
            if point_name in bbox:
                point = bbox[point_name]
                all_points.append({
                    'x': point[0],
                    'y': point[1],
                    'point_name': point_name
                })

    if len(all_points) < 2:
        return {'horizontal_error': 0, 'vertical_error': 0, 'total_points': len(all_points)}

    # 计算水平对齐误差（相同Y坐标的点）
    horizontal_errors = []
    tolerance = 5.0  # 5像素容差认为是同一水平线

    for i, point1 in enumerate(all_points):
        for j, point2 in enumerate(all_points):
            if i >= j:
                continue
            
            # 如果Y坐标相近，计算水平误差
            if abs(point1['y'] - point2['y']) <= tolerance:
                horizontal_errors.append(abs(point1['y'] - point2['y']))

    # 计算垂直对齐误差（相同X坐标的点）
    vertical_errors = []
    
    for i, point1 in enumerate(all_points):
        for j, point2 in enumerate(all_points):
            if i >= j:
                continue
            
            # 如果X坐标相近，计算垂直误差
            if abs(point1['x'] - point2['x']) <= tolerance:
                vertical_errors.append(abs(point1['x'] - point2['x']))

    # 计算平均误差
    avg_horizontal_error = sum(horizontal_errors) / len(horizontal_errors) if horizontal_errors else 0
    avg_vertical_error = sum(vertical_errors) / len(vertical_errors) if vertical_errors else 0

    return {
        'horizontal_error': avg_horizontal_error,
        'vertical_error': avg_vertical_error,
        'total_points': len(all_points),
        'horizontal_pairs': len(horizontal_errors),
        'vertical_pairs': len(vertical_errors)
    }


def test_single_file(annotation_file: Path, config: Dict[str, Any]) -> Dict[str, Any]:
    """
    测试单个文件

    Args:
        annotation_file: 标注文件路径
        config: 配置参数

    Returns:
        测试结果
    """
    print(f"\n测试文件: {annotation_file.name}")
    print(f"配置: {config}")
    
    # 查找对应图片
    image_file = None
    for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
        img_path = annotation_file.parent / f"{annotation_file.stem.replace('_table_annotation', '')}{ext}"
        if img_path.exists():
            image_file = str(img_path)
            break
    
    if not image_file:
        print("  ⚠️  未找到对应图片文件")
    
    # 加载原始数据计算质量
    with open(annotation_file, 'r', encoding='utf-8') as f:
        original_data = json.load(f)
    
    original_quality = calculate_alignment_quality(original_data['cells'])
    
    # 创建优化器
    optimizer = SimplifiedTableOptimizer(
        tolerance=config['tolerance'],
        adaptive_threshold=config['adaptive_threshold'],
        merge_threshold_factor=config['merge_threshold_factor'],
        alignment_strength=config['alignment_strength']
    )
    
    # 执行优化
    output_file = f"test_output_{annotation_file.name}"
    start_time = time.time()
    
    result = optimizer.optimize_table_annotation(
        str(annotation_file),
        output_file,
        image_file
    )
    
    processing_time = time.time() - start_time
    
    if not result['success']:
        return {
            'success': False,
            'error': result['error'],
            'processing_time': processing_time
        }
    
    # 加载优化后的数据计算质量
    with open(output_file, 'r', encoding='utf-8') as f:
        optimized_data = json.load(f)
    
    optimized_quality = calculate_alignment_quality(optimized_data['cells'])
    
    # 计算改进效果
    horizontal_improvement = 0
    vertical_improvement = 0
    
    if original_quality['horizontal_error'] > 0:
        horizontal_improvement = (original_quality['horizontal_error'] - optimized_quality['horizontal_error']) / original_quality['horizontal_error'] * 100
    
    if original_quality['vertical_error'] > 0:
        vertical_improvement = (original_quality['vertical_error'] - optimized_quality['vertical_error']) / original_quality['vertical_error'] * 100
    
    # 清理测试文件
    if os.path.exists(output_file):
        os.remove(output_file)
    
    test_result = {
        'success': True,
        'processing_time': processing_time,
        'cell_count': result['cell_count'],
        'adaptive_threshold': result.get('adaptive_threshold'),
        'original_quality': original_quality,
        'optimized_quality': optimized_quality,
        'horizontal_improvement': horizontal_improvement,
        'vertical_improvement': vertical_improvement,
        'config': config
    }
    
    print(f"  ✅ 处理成功")
    print(f"  📊 单元格数: {result['cell_count']}")
    print(f"  ⏱️  处理时间: {processing_time:.3f}s")
    if result.get('adaptive_threshold'):
        print(f"  🎯 自适应阈值: {result['adaptive_threshold']:.2f}px")
    print(f"  📈 水平改进: {horizontal_improvement:.1f}%")
    print(f"  📈 垂直改进: {vertical_improvement:.1f}%")
    
    return test_result


def run_comparison_test():
    """
    运行对比测试
    """
    print("🚀 简化优化器对比测试")
    print("=" * 60)
    
    # 查找测试文件
    test_files = []
    
    # 查找当前目录下的标注文件
    current_dir = Path('.')
    annotation_files = list(current_dir.glob('*_table_annotation.json'))
    
    if annotation_files:
        test_files.extend(annotation_files[:3])  # 最多测试3个文件
    
    # 如果当前目录没有，查找子目录
    if not test_files:
        for subdir in current_dir.iterdir():
            if subdir.is_dir():
                annotation_files = list(subdir.glob('*_table_annotation.json'))
                if annotation_files:
                    test_files.extend(annotation_files[:3])
                    break
    
    if not test_files:
        print("❌ 未找到测试文件")
        return
    
    print(f"找到 {len(test_files)} 个测试文件")
    
    # 测试配置
    configs = [
        {
            'name': '默认配置',
            'tolerance': 3.0,
            'adaptive_threshold': True,
            'merge_threshold_factor': 1.5,
            'alignment_strength': 0.8
        },
        {
            'name': '保守配置',
            'tolerance': 2.0,
            'adaptive_threshold': True,
            'merge_threshold_factor': 1.2,
            'alignment_strength': 0.6
        },
        {
            'name': '激进配置',
            'tolerance': 4.0,
            'adaptive_threshold': True,
            'merge_threshold_factor': 2.0,
            'alignment_strength': 1.0
        },
        {
            'name': '固定阈值',
            'tolerance': 3.0,
            'adaptive_threshold': False,
            'merge_threshold_factor': 1.5,
            'alignment_strength': 0.8
        }
    ]
    
    # 运行测试
    all_results = []
    
    for config in configs:
        print(f"\n🔧 测试配置: {config['name']}")
        print("-" * 40)
        
        config_results = []
        
        for test_file in test_files:
            result = test_single_file(test_file, config)
            if result['success']:
                config_results.append(result)
        
        if config_results:
            # 计算平均结果
            avg_horizontal_improvement = sum(r['horizontal_improvement'] for r in config_results) / len(config_results)
            avg_vertical_improvement = sum(r['vertical_improvement'] for r in config_results) / len(config_results)
            avg_processing_time = sum(r['processing_time'] for r in config_results) / len(config_results)
            
            summary = {
                'config': config,
                'test_count': len(config_results),
                'avg_horizontal_improvement': avg_horizontal_improvement,
                'avg_vertical_improvement': avg_vertical_improvement,
                'avg_processing_time': avg_processing_time,
                'results': config_results
            }
            
            all_results.append(summary)
            
            print(f"\n📊 {config['name']} 平均结果:")
            print(f"  测试文件数: {len(config_results)}")
            print(f"  平均水平改进: {avg_horizontal_improvement:.1f}%")
            print(f"  平均垂直改进: {avg_vertical_improvement:.1f}%")
            print(f"  平均处理时间: {avg_processing_time:.3f}s")
    
    # 生成最终报告
    print("\n" + "=" * 60)
    print("🏆 最终对比报告")
    print("=" * 60)
    
    if all_results:
        # 按综合改进效果排序
        all_results.sort(key=lambda x: (x['avg_horizontal_improvement'] + x['avg_vertical_improvement']) / 2, reverse=True)
        
        print("配置排名（按平均改进效果）:")
        for i, summary in enumerate(all_results, 1):
            avg_improvement = (summary['avg_horizontal_improvement'] + summary['avg_vertical_improvement']) / 2
            print(f"{i}. {summary['config']['name']}: {avg_improvement:.1f}% 改进")
        
        # 推荐最佳配置
        best_config = all_results[0]
        print(f"\n🎯 推荐配置: {best_config['config']['name']}")
        print(f"  参数: {best_config['config']}")
        print(f"  平均改进: {(best_config['avg_horizontal_improvement'] + best_config['avg_vertical_improvement']) / 2:.1f}%")
        print(f"  平均处理时间: {best_config['avg_processing_time']:.3f}s")
        
        # 保存详细报告
        report_file = "simplified_optimizer_test_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
    else:
        print("❌ 没有成功的测试结果")


def main():
    """主函数"""
    try:
        run_comparison_test()
    except Exception as e:
        print(f"测试失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
