#!/usr/bin/env python3
"""
表格标注优化器 - 简化高效的表格标注优化算法

主要功能:
1. 智能角点对齐，减少表格线分叉
2. 自适应阈值，基于图像和表格特征
3. 保留所有原始属性不变
4. 高效批量处理
5. 可配置参数和路径

优化策略：
- 简化算法逻辑，专注核心问题
- 智能阈值计算，避免过度复杂
- 渐进式优化，确保稳定性

作者: AI Assistant
版本: 4.0 (简化优化版)
更新日期: 2025-01-08
"""

import os
import json
import time
import math
import numpy as np
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
import argparse
from typing import List, Dict, Tuple, Optional, Any
from PIL import Image


# ==================== 配置区域 ====================
# 在这里修改处理参数
CONFIG = {
    # 输入输出路径配置
    'input_dir': "F:\workspace\datasets\Relabel_TabRecSet\chinese\curved_tables",           # 输入文件夹路径
    'output_dir': "F:\workspace\datasets\Relabel_TabRecSet\chinese\curved_tables_2", # 输出文件夹路径

    # 文件匹配模式
    'annotation_pattern': '*_table_annotation.json',  # 标注文件匹配模式
    'image_extensions': ['.jpg', '.jpeg', '.png', '.bmp', '.tiff'],  # 支持的图片格式

    # 算法参数（简化优化）
    'tolerance': 3.0,              # 基础容差阈值（像素）
    'adaptive_threshold': True,    # 启用智能自适应阈值
    'merge_threshold_factor': 1.5, # 合并阈值因子
    'alignment_strength': 0.8,     # 对齐强度 (0.0-1.0)
    'preserve_perspective': True,   # 保持透视变换

    # 处理参数
    'max_workers': 1,              # 并行处理线程数
    'copy_images': True,           # 是否复制图片文件到输出目录

    # 属性保护配置
    'preserve_attributes': ['quality', 'type'],  # 需要保护的属性列表
}
# ================================================


class SimplifiedTableOptimizer:
    """简化高效的表格标注优化器"""

    def __init__(self, tolerance: float = 3.0, adaptive_threshold: bool = True,
                 merge_threshold_factor: float = 1.5, alignment_strength: float = 0.8,
                 preserve_perspective: bool = True):
        """
        初始化优化器

        Args:
            tolerance: 基础容差阈值（像素）
            adaptive_threshold: 是否使用自适应阈值
            merge_threshold_factor: 合并阈值因子
            alignment_strength: 对齐强度 (0.0-1.0)
            preserve_perspective: 是否保持透视变换
        """
        self.base_tolerance = tolerance
        self.adaptive_threshold = adaptive_threshold
        self.merge_threshold_factor = merge_threshold_factor
        self.alignment_strength = alignment_strength
        self.preserve_perspective = preserve_perspective

        # 运行时变量
        self.tolerance = tolerance
        self.image_info = None
        self.cells = []
        self.table_ind = ""
        self.image_path = ""
        self.original_data = None

    def get_image_info(self, image_path: str) -> Optional[Dict[str, int]]:
        """
        获取图片信息

        Args:
            image_path: 图片文件路径

        Returns:
            包含图片宽度和高度的字典，失败时返回None
        """
        try:
            if image_path and Path(image_path).exists():
                with Image.open(image_path) as img:
                    return {'width': img.size[0], 'height': img.size[1]}
        except Exception as e:
            print(f"无法读取图片 {image_path}: {e}")
        return None

    def calculate_smart_threshold(self, img_width: int, img_height: int,
                                table_width: float, table_height: float) -> float:
        """
        智能计算自适应阈值

        Args:
            img_width: 图片宽度
            img_height: 图片高度
            table_width: 表格宽度
            table_height: 表格高度

        Returns:
            计算得到的智能阈值
        """
        if not self.adaptive_threshold:
            return self.base_tolerance

        # 1. 基于分辨率的基础因子
        total_pixels = img_width * img_height
        resolution_factor = math.sqrt(total_pixels / (1920 * 1080))  # 以1080p为基准
        resolution_factor = max(0.5, min(2.0, resolution_factor))

        # 2. 基于表格占比的尺寸因子
        if table_width > 0 and table_height > 0:
            table_area = table_width * table_height
            image_area = img_width * img_height
            area_ratio = table_area / image_area

            # 简化的尺寸因子计算
            if area_ratio > 0.8:
                size_factor = 0.7  # 大表格需要更精确
            elif area_ratio > 0.5:
                size_factor = 1.0  # 中等表格
            elif area_ratio > 0.2:
                size_factor = 1.3  # 小表格可以宽松一些
            else:
                size_factor = 1.5  # 很小的表格
        else:
            size_factor = 1.0

        # 3. 综合计算
        adaptive_threshold = self.base_tolerance * (0.6 * resolution_factor + 0.4 * size_factor)

        # 限制范围
        adaptive_threshold = max(1.0, min(10.0, adaptive_threshold))

        return adaptive_threshold

    def calculate_table_bounds(self, cells: List[Dict]) -> Tuple[float, float]:
        """
        计算表格边界尺寸

        Args:
            cells: 单元格列表

        Returns:
            表格宽度和高度的元组
        """
        if not cells:
            return 0.0, 0.0

        all_x = []
        all_y = []

        for cell in cells:
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                if point_name in bbox:
                    point = bbox[point_name]
                    all_x.append(point[0])
                    all_y.append(point[1])

        if not all_x or not all_y:
            return 0.0, 0.0

        width = max(all_x) - min(all_x)
        height = max(all_y) - min(all_y)

        return width, height

    def get_image_info(self, image_path: str) -> Optional[Dict[str, int]]:
        """
        获取图片信息

        Args:
            image_path: 图片文件路径

        Returns:
            包含图片宽度和高度的字典，失败时返回None
        """
        try:
            if image_path and Path(image_path).exists():
                with Image.open(image_path) as img:
                    return {'width': img.size[0], 'height': img.size[1]}
        except Exception as e:
            print(f"无法读取图片 {image_path}: {e}")
        return None

    def calculate_table_bounds(self, cells: List[Dict]) -> Tuple[float, float]:
        """
        计算表格边界尺寸

        Args:
            cells: 单元格列表

        Returns:
            表格宽度和高度的元组
        """
        if not cells:
            return 0.0, 0.0

        all_x = []
        all_y = []

        for cell in cells:
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                point = bbox[point_name]
                all_x.append(point[0])
                all_y.append(point[1])

        width = max(all_x) - min(all_x)
        height = max(all_y) - min(all_y)

        return width, height

    def load_annotation(self, json_path: str, image_path: Optional[str] = None):
        """
        加载表格标注文件

        Args:
            json_path: 标注文件路径
            image_path: 对应的图片文件路径（用于自适应阈值计算）
        """
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 保存原始数据以便保护所有属性
        self.original_data = data.copy()

        self.cells = data['cells']
        self.table_ind = data.get('table_ind', '')
        self.image_path = data.get('image_path', '')

        # 获取图片信息用于自适应阈值计算
        if image_path:
            self.image_info = self.get_image_info(image_path)

            # 计算表格边界用于自适应阈值
            if self.image_info:
                table_width, table_height = self.calculate_table_bounds(self.cells)
                self.tolerance = self.calculate_smart_threshold(
                    self.image_info['width'],
                    self.image_info['height'],
                    table_width,
                    table_height
                )

                print(f"智能阈值计算结果: {self.tolerance:.2f}px")
                print(f"  图片尺寸: {self.image_info['width']}x{self.image_info['height']}")
                print(f"  表格尺寸: {table_width:.1f}x{table_height:.1f}")
                if table_width > 0 and table_height > 0:
                    area_ratio = (table_width * table_height) / (self.image_info['width'] * self.image_info['height'])
                    print(f"  表格占比: {area_ratio:.1%}")
            else:
                print(f"无法获取图片信息，使用基础阈值: {self.base_tolerance}")
                self.tolerance = self.base_tolerance

        print(f"加载了 {len(self.cells)} 个单元格")

    def find_nearby_points(self) -> List[List[Dict]]:
        """
        查找相近的角点组

        Returns:
            相近角点组列表
        """
        all_points = []

        # 收集所有角点
        for cell_idx, cell in enumerate(self.cells):
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                if point_name in bbox:
                    point = bbox[point_name]
                    all_points.append({
                        'coords': point,
                        'cell_idx': cell_idx,
                        'point_name': point_name,
                        'x': point[0],
                        'y': point[1]
                    })

        # 使用合并阈值查找相近点组
        merge_threshold = self.tolerance * self.merge_threshold_factor
        point_groups = []
        used_indices = set()

        for i, point1 in enumerate(all_points):
            if i in used_indices:
                continue

            group = [point1]
            used_indices.add(i)

            for j, point2 in enumerate(all_points):
                if j in used_indices:
                    continue

                # 计算欧几里得距离
                distance = math.sqrt(
                    (point1['x'] - point2['x'])**2 +
                    (point1['y'] - point2['y'])**2
                )

                if distance <= merge_threshold:
                    group.append(point2)
                    used_indices.add(j)

            if len(group) > 1:
                point_groups.append(group)

        return point_groups

    def align_nearby_points(self):
        """
        对齐相近的角点
        """
        point_groups = self.find_nearby_points()

        if not point_groups:
            print("  未发现需要对齐的相近角点")
            return

        print(f"  发现 {len(point_groups)} 组相近角点")

        aligned_count = 0
        for group in point_groups:
            if len(group) < 2:
                continue

            # 计算组的加权中心
            center_x = sum(p['x'] for p in group) / len(group)
            center_y = sum(p['y'] for p in group) / len(group)

            # 应用对齐强度
            for point in group:
                original_x = point['x']
                original_y = point['y']

                # 计算新坐标（应用对齐强度）
                new_x = original_x + (center_x - original_x) * self.alignment_strength
                new_y = original_y + (center_y - original_y) * self.alignment_strength

                # 更新坐标
                self.cells[point['cell_idx']]['bbox'][point['point_name']] = [new_x, new_y]
                aligned_count += 1

        if aligned_count > 0:
            print(f"  对齐了 {aligned_count} 个角点")

    def optimize_table_annotation(self, input_file: str, output_file: str,
                                image_file: Optional[str] = None) -> Dict[str, Any]:
        """
        优化表格标注

        Args:
            input_file: 输入标注文件路径
            output_file: 输出标注文件路径
            image_file: 对应的图片文件路径

        Returns:
            处理结果字典
        """
        try:
            print(f"开始优化: {Path(input_file).name}")

            # 加载标注数据
            self.load_annotation(input_file, image_file)

            # 执行优化
            print("执行角点对齐优化...")
            self.align_nearby_points()

            # 保存结果
            self.save_annotation(output_file)

            result = {
                'success': True,
                'cell_count': len(self.cells),
                'adaptive_threshold': self.tolerance if self.adaptive_threshold else None,
                'image_info': self.image_info
            }

            print(f"优化完成: {Path(output_file).name}")
            return result

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'cell_count': 0
            }

    def save_annotation(self, output_file: str):
        """
        保存优化后的标注文件，保护所有原始属性

        Args:
            output_file: 输出文件路径
        """
        # 使用原始数据作为基础，只更新cells
        output_data = self.original_data.copy()
        output_data['cells'] = self.cells

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)

# 删除复杂的旧方法，使用简化的智能阈值计算

    def load_annotation(self, json_path: str, image_path: Optional[str] = None):
        """
        加载表格标注文件

        Args:
            json_path: 标注文件路径
            image_path: 对应的图片文件路径（用于自适应阈值计算）
        """
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 保存原始数据以便保护所有属性
        self.original_data = data.copy()

        self.cells = data['cells']
        self.table_ind = data.get('table_ind', '')
        self.image_path = data.get('image_path', '')

        # 获取图片信息用于自适应阈值计算
        if image_path:
            self.image_info = self.get_image_info(image_path)

            # 检测图片旋转角度
            if self.angle_correction:
                self.detected_rotation = self.detect_image_rotation(image_path)

            # 计算表格边界用于自适应阈值
            if self.image_info:
                table_width, table_height = self.calculate_table_bounds(self.cells)
                self.tolerance = self.calculate_adaptive_threshold(
                    self.image_info['width'],
                    self.image_info['height'],
                    table_width,
                    table_height
                )

                # 显示详细的阈值计算信息
                print(f"自适应阈值计算结果: {self.tolerance:.2f}")
                print(f"  图片尺寸: {self.image_info['width']}x{self.image_info['height']} ({self.image_info['width'] * self.image_info['height']:,} 像素)")
                print(f"  表格尺寸: {table_width:.1f}x{table_height:.1f}")

                if hasattr(self, 'area_ratio_info'):
                    area_info = self.area_ratio_info
                    print(f"  表格面积占比: {area_info['area_ratio']:.1%}")
                    print(f"  像素密度: {area_info.get('table_pixel_density', 0):.1%}")
                    if 'aspect_similarity' in area_info:
                        print(f"  形状相似度: {area_info['aspect_similarity']:.2f}")
                    print(f"  面积因子: {area_info['final_size_factor']:.2f}")

                if hasattr(self, 'threshold_calculation_info'):
                    calc_info = self.threshold_calculation_info
                    print(f"  因子详情: 分辨率={calc_info['resolution_factor']:.2f}, 面积={calc_info['size_factor']:.2f}, 密度={calc_info['density_factor']:.2f}, 质量={calc_info['quality_factor']:.2f}")
                    print(f"  综合因子: {calc_info['combined_factor']:.2f}")
            else:
                print(f"无法获取图片信息，使用基础阈值: {self.base_tolerance}")
                self.tolerance = self.base_tolerance

        print(f"加载了 {len(self.cells)} 个单元格")

    def build_grid_structure(self):
        """构建表格的逻辑网格结构"""
        self.grid_structure = {}

        for cell in self.cells:
            lloc = cell['lloc']
            for r in range(lloc['start_row'], lloc['end_row'] + 1):
                for c in range(lloc['start_col'], lloc['end_col'] + 1):
                    if (r, c) not in self.grid_structure:
                        self.grid_structure[(r, c)] = []
                    self.grid_structure[(r, c)].append(cell)

    def get_shared_boundary_points(self, direction: str) -> List[Tuple[List[float], List[int]]]:
        """
        获取共享边界上的角点

        Args:
            direction: 'horizontal' 或 'vertical'

        Returns:
            (点坐标, 相关单元格索引列表) 的列表
        """
        boundary_points = []

        if direction == 'horizontal':
            # 处理水平边界（行之间的边界）
            rows = sorted(set(r for r, c in self.grid_structure.keys()))
            cols = sorted(set(c for r, c in self.grid_structure.keys()))

            for row_idx in range(len(rows) - 1):
                current_row = rows[row_idx]
                next_row = rows[row_idx + 1]

                for col in cols:
                    current_cells = self.grid_structure.get((current_row, col), [])
                    next_cells = self.grid_structure.get((next_row, col), [])

                    if current_cells and next_cells:
                        for curr_cell in current_cells:
                            for next_cell in next_cells:
                                if (curr_cell['lloc']['end_row'] == current_row and
                                    next_cell['lloc']['start_row'] == next_row):
                                    # 收集共享边界上的点
                                    curr_idx = self.cells.index(curr_cell)
                                    next_idx = self.cells.index(next_cell)

                                    # 当前单元格的下边界点
                                    boundary_points.append((curr_cell['bbox']['p3'], [curr_idx]))
                                    boundary_points.append((curr_cell['bbox']['p4'], [curr_idx]))

                                    # 下一个单元格的上边界点
                                    boundary_points.append((next_cell['bbox']['p1'], [next_idx]))
                                    boundary_points.append((next_cell['bbox']['p2'], [next_idx]))

        elif direction == 'vertical':
            # 处理垂直边界（列之间的边界）
            rows = sorted(set(r for r, c in self.grid_structure.keys()))
            cols = sorted(set(c for r, c in self.grid_structure.keys()))

            for col_idx in range(len(cols) - 1):
                current_col = cols[col_idx]
                next_col = cols[col_idx + 1]

                for row in rows:
                    current_cells = self.grid_structure.get((row, current_col), [])
                    next_cells = self.grid_structure.get((row, next_col), [])

                    if current_cells and next_cells:
                        for curr_cell in current_cells:
                            for next_cell in next_cells:
                                if (curr_cell['lloc']['end_col'] == current_col and
                                    next_cell['lloc']['start_col'] == next_col):
                                    # 收集共享边界上的点
                                    curr_idx = self.cells.index(curr_cell)
                                    next_idx = self.cells.index(next_cell)

                                    # 当前单元格的右边界点
                                    boundary_points.append((curr_cell['bbox']['p2'], [curr_idx]))
                                    boundary_points.append((curr_cell['bbox']['p3'], [curr_idx]))

                                    # 下一个单元格的左边界点
                                    boundary_points.append((next_cell['bbox']['p1'], [next_idx]))
                                    boundary_points.append((next_cell['bbox']['p4'], [next_idx]))

        return boundary_points

    def align_boundary_points(self, boundary_points: List[Tuple[List[float], List[int]]],
                            coordinate_index: int):
        """
        改进的边界点对齐算法

        Args:
            boundary_points: 边界点列表
            coordinate_index: 坐标索引（0=x, 1=y）
        """
        if not boundary_points:
            return

        # 使用改进的聚类算法
        clusters = self._improved_clustering(boundary_points, coordinate_index)

        # 对每个聚类进行智能对齐
        aligned_count = 0
        for cluster in clusters:
            if len(cluster) < 2:
                continue

            # 使用加权平均而不是简单平均
            aligned_coord = self._calculate_weighted_average(cluster, coordinate_index)

            # 更新所有相关单元格的角点（保守模式）
            for point, cell_indices, _ in cluster:
                for cell_idx in cell_indices:
                    cell = self.cells[cell_idx]
                    bbox = cell['bbox']

                    # 找到对应的角点并更新
                    for point_name in ['p1', 'p2', 'p3', 'p4']:
                        if (abs(bbox[point_name][0] - point[0]) < 0.1 and
                            abs(bbox[point_name][1] - point[1]) < 0.1):

                            # 保守更新：创建新坐标
                            new_coords = bbox[point_name].copy()
                            new_coords[coordinate_index] = aligned_coord

                            # 使用保守更新方法
                            self.conservative_update_point(cell_idx, point_name, new_coords)
                            aligned_count += 1

        if aligned_count > 0:
            print(f"  对齐了 {aligned_count} 个边界点")

    def _improved_clustering(self, boundary_points: List[Tuple[List[float], List[int]]],
                           coordinate_index: int) -> List[List[Tuple]]:
        """
        改进的聚类算法，使用动态阈值

        Args:
            boundary_points: 边界点列表
            coordinate_index: 坐标索引

        Returns:
            聚类结果
        """
        if not boundary_points:
            return []

        # 按坐标值排序
        sorted_points = sorted(enumerate(boundary_points),
                             key=lambda x: x[1][0][coordinate_index])

        clusters = []
        current_cluster = []

        for i, (orig_idx, (point, cells)) in enumerate(sorted_points):
            if not current_cluster:
                current_cluster = [(point, cells, orig_idx)]
            else:
                # 计算与当前聚类的距离
                last_point = current_cluster[-1][0]
                distance = abs(point[coordinate_index] - last_point[coordinate_index])

                # 动态阈值：考虑聚类内的方差
                dynamic_threshold = self._calculate_dynamic_threshold(current_cluster, coordinate_index)

                if distance <= dynamic_threshold:
                    current_cluster.append((point, cells, orig_idx))
                else:
                    # 开始新聚类
                    if len(current_cluster) > 1:
                        clusters.append(current_cluster)
                    current_cluster = [(point, cells, orig_idx)]

        # 添加最后一个聚类
        if len(current_cluster) > 1:
            clusters.append(current_cluster)

        return clusters

    def _calculate_dynamic_threshold(self, cluster: List[Tuple], coordinate_index: int) -> float:
        """
        计算动态阈值

        Args:
            cluster: 当前聚类
            coordinate_index: 坐标索引

        Returns:
            动态阈值
        """
        if len(cluster) < 2:
            return self.tolerance

        coords = [point[coordinate_index] for point, _, _ in cluster]
        variance = sum((x - sum(coords)/len(coords))**2 for x in coords) / len(coords)
        std_dev = math.sqrt(variance)

        # 基于标准差调整阈值
        return max(self.tolerance * 0.5, min(self.tolerance * 2.0, self.tolerance + std_dev))

    def _calculate_weighted_average(self, cluster: List[Tuple], coordinate_index: int) -> float:
        """
        计算加权平均坐标

        Args:
            cluster: 聚类点
            coordinate_index: 坐标索引

        Returns:
            加权平均坐标
        """
        total_weight = 0
        weighted_sum = 0

        for point, cell_indices, _ in cluster:
            # 权重基于相关单元格数量
            weight = len(cell_indices)
            weighted_sum += point[coordinate_index] * weight
            total_weight += weight

        return weighted_sum / total_weight if total_weight > 0 else sum(point[coordinate_index] for point, _, _ in cluster) / len(cluster)

    def align_row_boundaries(self):
        """对齐行边界（水平对齐）"""
        boundary_points = self.get_shared_boundary_points('horizontal')
        self.align_boundary_points(boundary_points, 1)  # 对齐Y坐标

    def align_column_boundaries(self):
        """对齐列边界（垂直对齐）"""
        boundary_points = self.get_shared_boundary_points('vertical')
        self.align_boundary_points(boundary_points, 0)  # 对齐X坐标

    def fine_tune_nearby_points(self):
        """改进的微调相近角点算法"""
        if self.quality_aware:
            # 使用质量感知微调
            self.quality_aware_fine_tuning()
        else:
            # 使用传统多级微调
            all_points = []

            # 收集所有角点
            for cell_idx, cell in enumerate(self.cells):
                bbox = cell['bbox']
                for point_name in ['p1', 'p2', 'p3', 'p4']:
                    point = bbox[point_name]
                    all_points.append({
                        'coords': point,
                        'cell_idx': cell_idx,
                        'point_name': point_name
                    })

            # 使用多级微调策略
            self._multi_level_fine_tuning(all_points)

    def _multi_level_fine_tuning(self, all_points: List[Dict]):
        """
        多级微调策略

        Args:
            all_points: 所有角点列表
        """
        # 第一级：精确微调（很小的阈值）
        micro_threshold = self.tolerance * 0.3
        aligned_count_1 = self._fine_tune_with_threshold(all_points, micro_threshold, "精确")

        # 第二级：中等微调
        medium_threshold = self.tolerance * 0.6
        aligned_count_2 = self._fine_tune_with_threshold(all_points, medium_threshold, "中等")

        total_aligned = aligned_count_1 + aligned_count_2
        if total_aligned > 0:
            print(f"微调了 {total_aligned} 个相近角点 (精确: {aligned_count_1}, 中等: {aligned_count_2})")

    def _fine_tune_with_threshold(self, all_points: List[Dict], threshold: float, level: str) -> int:
        """
        使用指定阈值进行微调

        Args:
            all_points: 所有角点列表
            threshold: 微调阈值
            level: 微调级别名称

        Returns:
            对齐的点数
        """
        aligned_count = 0
        used_indices = set()

        for i, point1 in enumerate(all_points):
            if i in used_indices:
                continue

            cluster = [point1]
            used_indices.add(i)

            for j, point2 in enumerate(all_points):
                if j in used_indices:
                    continue

                # 计算欧几里得距离
                dist = math.sqrt(
                    (point1['coords'][0] - point2['coords'][0])**2 +
                    (point1['coords'][1] - point2['coords'][1])**2
                )

                if dist <= threshold:
                    cluster.append(point2)
                    used_indices.add(j)

            # 如果聚类包含多个点，进行智能对齐
            if len(cluster) > 1:
                # 使用加权重心计算
                center_x, center_y = self._calculate_cluster_center(cluster)

                # 保守地更新所有点到重心位置
                for point in cluster:
                    new_coords = [center_x, center_y]
                    if self.conservative_update_point(point['cell_idx'], point['point_name'], new_coords):
                        aligned_count += 1

        return aligned_count

    def _calculate_cluster_center(self, cluster: List[Dict]) -> Tuple[float, float]:
        """
        计算聚类的加权重心

        Args:
            cluster: 角点聚类

        Returns:
            重心坐标 (x, y)
        """
        # 简单平均（可以根据需要添加权重）
        center_x = sum(p['coords'][0] for p in cluster) / len(cluster)
        center_y = sum(p['coords'][1] for p in cluster) / len(cluster)

        return center_x, center_y

    def detect_overlapping_points(self) -> List[List[Dict]]:
        """
        检测重叠或极近的角点（基于质量样本分析）

        Returns:
            重叠角点组列表
        """
        if not self.quality_aware:
            return []

        all_points = []

        # 收集所有角点
        for cell_idx, cell in enumerate(self.cells):
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                point = bbox[point_name]
                all_points.append({
                    'coords': point,
                    'cell_idx': cell_idx,
                    'point_name': point_name
                })

        # 检测重叠点组
        overlap_groups = []
        used_indices = set()

        for i, point1 in enumerate(all_points):
            if i in used_indices:
                continue

            group = [point1]
            used_indices.add(i)

            for j, point2 in enumerate(all_points):
                if j in used_indices:
                    continue

                # 计算距离
                dist = math.sqrt(
                    (point1['coords'][0] - point2['coords'][0])**2 +
                    (point1['coords'][1] - point2['coords'][1])**2
                )

                if dist <= self.overlap_threshold:
                    group.append(point2)
                    used_indices.add(j)

            if len(group) > 1:
                overlap_groups.append(group)

        return overlap_groups

    def resolve_overlapping_points(self):
        """
        解决重叠角点问题（基于质量样本分析）
        """
        if not self.quality_aware:
            return

        overlap_groups = self.detect_overlapping_points()

        if not overlap_groups:
            return

        print(f"  发现 {len(overlap_groups)} 组重叠角点")

        resolved_count = 0
        for group in overlap_groups:
            if len(group) < 2:
                continue

            # 计算组内角点的加权中心
            center_x = sum(p['coords'][0] for p in group) / len(group)
            center_y = sum(p['coords'][1] for p in group) / len(group)

            # 更新所有重叠点到中心位置
            for point in group:
                cell = self.cells[point['cell_idx']]
                cell['bbox'][point['point_name']][0] = center_x
                cell['bbox'][point['point_name']][1] = center_y
                resolved_count += 1

        print(f"  解决了 {resolved_count} 个重叠角点")

    def calculate_cell_regularity(self, cell: Dict) -> float:
        """
        计算单元格的规整度（基于质量样本分析）

        Args:
            cell: 单元格数据

        Returns:
            规整度分数 (0-1)
        """
        bbox = cell.get('bbox', {})
        if not all(p in bbox for p in ['p1', 'p2', 'p3', 'p4']):
            return 0.0

        # 获取四个角点
        p1, p2, p3, p4 = [bbox[p] for p in ['p1', 'p2', 'p3', 'p4']]

        # 计算四条边的长度
        side1 = math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
        side2 = math.sqrt((p3[0] - p2[0])**2 + (p3[1] - p2[1])**2)
        side3 = math.sqrt((p4[0] - p3[0])**2 + (p4[1] - p3[1])**2)
        side4 = math.sqrt((p1[0] - p4[0])**2 + (p1[1] - p4[1])**2)

        # 对边长度比
        if side1 > 0 and side3 > 0:
            ratio1 = min(side1, side3) / max(side1, side3)
        else:
            ratio1 = 0

        if side2 > 0 and side4 > 0:
            ratio2 = min(side2, side4) / max(side2, side4)
        else:
            ratio2 = 0

        return (ratio1 + ratio2) / 2

    def quality_aware_fine_tuning(self):
        """
        基于质量样本特征的精细调整
        """
        print("执行质量感知微调...")

        # 1. 首先解决重叠角点
        self.resolve_overlapping_points()

        # 2. 基于规整度进行分组调整
        high_regularity_cells = []
        low_regularity_cells = []

        for cell in self.cells:
            regularity = self.calculate_cell_regularity(cell)
            if regularity >= self.regularity_threshold:
                high_regularity_cells.append(cell)
            else:
                low_regularity_cells.append(cell)

        print(f"  高规整度单元格: {len(high_regularity_cells)}")
        print(f"  低规整度单元格: {len(low_regularity_cells)}")

        # 3. 对高规整度单元格使用更精确的对齐
        if high_regularity_cells:
            self._fine_tune_regular_cells(high_regularity_cells)

        # 4. 对低规整度单元格使用更宽松的对齐
        if low_regularity_cells:
            self._fine_tune_irregular_cells(low_regularity_cells)

    def _fine_tune_regular_cells(self, cells: List[Dict]):
        """对规整单元格进行精确微调"""
        # 使用更小的阈值
        precise_threshold = self.tolerance * 0.3

        all_points = []
        for cell in cells:
            cell_idx = self.cells.index(cell)
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                point = bbox[point_name]
                all_points.append({
                    'coords': point,
                    'cell_idx': cell_idx,
                    'point_name': point_name
                })

        aligned_count = self._fine_tune_with_threshold(all_points, precise_threshold, "精确")
        print(f"  精确微调了 {aligned_count} 个规整单元格角点")

    def _fine_tune_irregular_cells(self, cells: List[Dict]):
        """对不规整单元格进行宽松微调"""
        # 使用更大的阈值
        loose_threshold = self.tolerance * 0.8

        all_points = []
        for cell in cells:
            cell_idx = self.cells.index(cell)
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                point = bbox[point_name]
                all_points.append({
                    'coords': point,
                    'cell_idx': cell_idx,
                    'point_name': point_name
                })

        aligned_count = self._fine_tune_with_threshold(all_points, loose_threshold, "宽松")
        print(f"  宽松微调了 {aligned_count} 个不规整单元格角点")

    def detect_image_rotation(self, image_path: str) -> float:
        """
        检测图片旋转角度

        Args:
            image_path: 图片路径

        Returns:
            检测到的旋转角度（度）
        """
        if not self.angle_correction:
            return 0.0

        try:
            import cv2

            # 读取图片
            image = cv2.imread(image_path)
            if image is None:
                print(f"无法读取图片: {image_path}")
                return 0.0

            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # 边缘检测
            edges = cv2.Canny(gray, 50, 150, apertureSize=3)

            # 霍夫线变换检测直线
            lines = cv2.HoughLines(edges, 1, math.pi/180, threshold=100)

            if lines is None:
                print("未检测到足够的直线")
                return 0.0

            # 分析线条角度
            angles = []
            for line in lines:
                rho, theta = line[0]
                angle = theta * 180 / math.pi

                # 将角度标准化到0-90度范围
                if angle > 90:
                    angle = angle - 180
                elif angle < -90:
                    angle = angle + 180

                angles.append(angle)

            # 找到主要的水平方向
            horizontal_angles = [a for a in angles if abs(a) < 25]

            if horizontal_angles:
                avg_horizontal = sum(horizontal_angles) / len(horizontal_angles)
                rotation_angle = -avg_horizontal  # 负号表示需要反向旋转来校正
                print(f"检测到图片旋转角度: {rotation_angle:.2f}度")
                return rotation_angle

            return 0.0

        except ImportError:
            print("OpenCV未安装，跳过角度检测")
            return 0.0
        except Exception as e:
            print(f"角度检测失败: {e}")
            return 0.0

    def calculate_line_angle(self, p1: List[float], p2: List[float]) -> float:
        """
        计算两点间直线的角度

        Args:
            p1: 起点坐标
            p2: 终点坐标

        Returns:
            角度（度）
        """
        dx = p2[0] - p1[0]
        dy = p2[1] - p1[1]

        if abs(dx) < 1e-6:  # 垂直线
            return 90.0

        angle = math.atan2(dy, dx) * 180 / math.pi
        return angle

    def find_shared_points(self) -> Dict[str, List[Dict]]:
        """
        改进的共享角点检测，使用更精确的聚类方法和逻辑关系验证

        Returns:
            共享点字典，键为坐标字符串，值为使用该点的单元格和角点信息
        """
        all_points = []

        # 收集所有角点
        for cell_idx, cell in enumerate(self.cells):
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                if point_name in bbox:
                    coords = bbox[point_name]
                    all_points.append({
                        'cell_idx': cell_idx,
                        'point_name': point_name,
                        'coords': coords,
                        'x': coords[0],
                        'y': coords[1],
                        'lloc': cell['lloc']  # 添加逻辑位置信息
                    })

        # 使用改进的聚类算法，结合距离和逻辑关系
        shared_groups = self._cluster_shared_points_improved(all_points)

        # 转换为字典格式
        shared_points = {}
        for group_idx, group in enumerate(shared_groups):
            if len(group) > 1:  # 只保留真正共享的点
                # 计算组的中心坐标作为键
                center_x = sum(p['x'] for p in group) / len(group)
                center_y = sum(p['y'] for p in group) / len(group)
                coord_key = f"group_{group_idx}_{center_x:.1f},{center_y:.1f}"

                shared_points[coord_key] = group

        return shared_points

    def _cluster_shared_points_improved(self, all_points: List[Dict]) -> List[List[Dict]]:
        """
        改进的共享点聚类算法，结合距离和逻辑关系

        Args:
            all_points: 所有角点列表

        Returns:
            聚类结果
        """
        if not all_points:
            return []

        # 使用更大的阈值来捕获更多潜在的共享点
        cluster_threshold = max(self.tolerance, 5.0)  # 至少5像素

        clusters = []
        used_indices = set()

        for i, point1 in enumerate(all_points):
            if i in used_indices:
                continue

            cluster = [point1]
            used_indices.add(i)

            for j, point2 in enumerate(all_points):
                if j in used_indices:
                    continue

                # 计算欧几里得距离
                distance = math.sqrt((point1['x'] - point2['x'])**2 +
                                   (point1['y'] - point2['y'])**2)

                # 检查是否应该共享（距离 + 逻辑关系）
                if distance <= cluster_threshold and self._should_share_point(point1, point2):
                    cluster.append(point2)
                    used_indices.add(j)

            clusters.append(cluster)

        return clusters

    def _should_share_point(self, point1: Dict, point2: Dict) -> bool:
        """
        判断两个角点是否应该共享坐标

        Args:
            point1: 第一个角点
            point2: 第二个角点

        Returns:
            是否应该共享
        """
        # 如果是同一个单元格的角点，不应该共享
        if point1['cell_idx'] == point2['cell_idx']:
            return False

        lloc1 = point1['lloc']
        lloc2 = point2['lloc']

        # 检查是否是相邻单元格的共享角点
        # 水平相邻
        if (lloc1['end_col'] + 1 == lloc2['start_col'] or
            lloc2['end_col'] + 1 == lloc1['start_col']):
            # 检查行范围是否有重叠
            row_overlap = not (lloc1['end_row'] < lloc2['start_row'] or
                             lloc2['end_row'] < lloc1['start_row'])
            if row_overlap:
                return True

        # 垂直相邻
        if (lloc1['end_row'] + 1 == lloc2['start_row'] or
            lloc2['end_row'] + 1 == lloc1['start_row']):
            # 检查列范围是否有重叠
            col_overlap = not (lloc1['end_col'] < lloc2['start_col'] or
                             lloc2['end_col'] < lloc1['start_col'])
            if col_overlap:
                return True

        # 对角相邻（角点共享）
        if ((lloc1['end_row'] + 1 == lloc2['start_row'] or
             lloc2['end_row'] + 1 == lloc1['start_row']) and
            (lloc1['end_col'] + 1 == lloc2['start_col'] or
             lloc2['end_col'] + 1 == lloc1['start_col'])):
            return True

        return False

    def align_shared_points_precisely(self):
        """
        精确对齐共享角点，确保相邻单元格共享同一个坐标
        使用强制性对齐，不受保守模式限制
        """
        shared_points = self.find_shared_points()

        if not shared_points:
            print("  未发现需要对齐的共享角点")
            return

        print(f"  发现 {len(shared_points)} 组共享角点")

        aligned_groups = 0
        total_aligned_points = 0

        for _, point_group in shared_points.items():
            if len(point_group) < 2:
                continue

            # 计算组内所有点的最优中心坐标
            center_coords = self._calculate_optimal_center(point_group)

            # 检查是否需要对齐
            max_distance = max(
                math.sqrt((p['x'] - center_coords[0])**2 + (p['y'] - center_coords[1])**2)
                for p in point_group
            )

            # 降低精度要求，但确保强制对齐
            if max_distance > 0.1:  # 0.1像素的精度要求
                aligned_count = 0

                for point in point_group:
                    # 强制性更新，绕过保守模式限制
                    self._force_update_point(
                        point['cell_idx'],
                        point['point_name'],
                        center_coords
                    )
                    aligned_count += 1

                if aligned_count > 0:
                    aligned_groups += 1
                    total_aligned_points += aligned_count

        if total_aligned_points > 0:
            print(f"  强制对齐了 {aligned_groups} 组共享点，共 {total_aligned_points} 个角点")
        else:
            print("  所有共享点已经精确对齐")

    def _force_update_point(self, cell_idx: int, point_name: str, new_coords: List[float]):
        """
        强制更新点坐标，不受保守模式限制

        Args:
            cell_idx: 单元格索引
            point_name: 点名称
            new_coords: 新坐标
        """
        self.cells[cell_idx]['bbox'][point_name] = new_coords.copy()

    def _calculate_optimal_center(self, point_group: List[Dict]) -> List[float]:
        """
        计算点组的最优中心坐标，考虑权重和稳定性

        Args:
            point_group: 点组

        Returns:
            最优中心坐标
        """
        if not point_group:
            return [0.0, 0.0]

        # 使用加权平均，权重基于单元格的重要性
        total_weight = 0
        weighted_x = 0
        weighted_y = 0

        for point in point_group:
            # 计算权重：基于单元格大小和位置
            cell = self.cells[point['cell_idx']]
            bbox = cell['bbox']

            # 计算单元格面积作为权重
            p1, p2, p3, p4 = [bbox[p] for p in ['p1', 'p2', 'p3', 'p4']]
            area = abs((p1[0] - p3[0]) * (p2[1] - p4[1]))
            weight = max(1.0, math.sqrt(area) / 100)  # 面积越大权重越高

            weighted_x += point['x'] * weight
            weighted_y += point['y'] * weight
            total_weight += weight

        if total_weight > 0:
            center_x = weighted_x / total_weight
            center_y = weighted_y / total_weight
        else:
            # 回退到简单平均
            center_x = sum(p['x'] for p in point_group) / len(point_group)
            center_y = sum(p['y'] for p in point_group) / len(point_group)

        return [center_x, center_y]

    def _calculate_group_center(self, point_group: List[Dict]) -> List[float]:
        """
        计算点组的加权中心

        Args:
            point_group: 点组

        Returns:
            中心坐标
        """
        if not point_group:
            return [0.0, 0.0]

        # 简单平均（可以根据需要添加权重）
        center_x = sum(p['x'] for p in point_group) / len(point_group)
        center_y = sum(p['y'] for p in point_group) / len(point_group)

        return [center_x, center_y]

    def enforce_grid_consistency(self):
        """
        强制网格一致性，确保相邻单元格的边界完全对齐
        """
        print("  强制网格一致性...")

        # 1. 首先对齐所有共享点
        self.align_shared_points_precisely()

        # 2. 然后确保行列边界的一致性
        self._enforce_row_consistency()
        self._enforce_column_consistency()

    def _enforce_row_consistency(self):
        """
        强制行边界一致性
        """
        if not hasattr(self, 'grid_structure') or not self.grid_structure:
            return

        rows = sorted(set(r for r, c in self.grid_structure.keys()))

        for row in rows:
            # 获取该行的所有单元格
            row_cells = []
            for (r, c), cells in self.grid_structure.items():
                if r == row:
                    row_cells.extend(cells)

            if len(row_cells) < 2:
                continue

            # 对齐上边界
            top_coords = []
            bottom_coords = []

            for cell in row_cells:
                bbox = cell['bbox']
                if all(p in bbox for p in ['p1', 'p2', 'p3', 'p4']):
                    top_coords.extend([bbox['p1'][1], bbox['p2'][1]])
                    bottom_coords.extend([bbox['p3'][1], bbox['p4'][1]])

            # 计算平均Y坐标
            if top_coords:
                avg_top_y = sum(top_coords) / len(top_coords)
                self._align_row_boundary(row_cells, 'top', avg_top_y)

            if bottom_coords:
                avg_bottom_y = sum(bottom_coords) / len(bottom_coords)
                self._align_row_boundary(row_cells, 'bottom', avg_bottom_y)

    def _enforce_column_consistency(self):
        """
        强制列边界一致性
        """
        if not hasattr(self, 'grid_structure') or not self.grid_structure:
            return

        cols = sorted(set(c for r, c in self.grid_structure.keys()))

        for col in cols:
            # 获取该列的所有单元格
            col_cells = []
            for (r, c), cells in self.grid_structure.items():
                if c == col:
                    col_cells.extend(cells)

            if len(col_cells) < 2:
                continue

            # 对齐左右边界
            left_coords = []
            right_coords = []

            for cell in col_cells:
                bbox = cell['bbox']
                if all(p in bbox for p in ['p1', 'p2', 'p3', 'p4']):
                    left_coords.extend([bbox['p1'][0], bbox['p4'][0]])
                    right_coords.extend([bbox['p2'][0], bbox['p3'][0]])

            # 计算平均X坐标
            if left_coords:
                avg_left_x = sum(left_coords) / len(left_coords)
                self._align_column_boundary(col_cells, 'left', avg_left_x)

            if right_coords:
                avg_right_x = sum(right_coords) / len(right_coords)
                self._align_column_boundary(col_cells, 'right', avg_right_x)

    def _align_row_boundary(self, row_cells: List[Dict], boundary: str, target_y: float):
        """
        对齐行边界

        Args:
            row_cells: 行内单元格
            boundary: 边界类型（'top' 或 'bottom'）
            target_y: 目标Y坐标
        """
        point_names = ['p1', 'p2'] if boundary == 'top' else ['p3', 'p4']

        for cell in row_cells:
            bbox = cell['bbox']
            cell_idx = self.cells.index(cell)

            for point_name in point_names:
                if point_name in bbox:
                    current_coords = bbox[point_name]
                    new_coords = [current_coords[0], target_y]

                    # 检查调整是否合理
                    if abs(target_y - current_coords[1]) <= self.tolerance:
                        self.conservative_update_point(cell_idx, point_name, new_coords)

    def _align_column_boundary(self, col_cells: List[Dict], boundary: str, target_x: float):
        """
        对齐列边界

        Args:
            col_cells: 列内单元格
            boundary: 边界类型（'left' 或 'right'）
            target_x: 目标X坐标
        """
        point_names = ['p1', 'p4'] if boundary == 'left' else ['p2', 'p3']

        for cell in col_cells:
            bbox = cell['bbox']
            cell_idx = self.cells.index(cell)

            for point_name in point_names:
                if point_name in bbox:
                    current_coords = bbox[point_name]
                    new_coords = [target_x, current_coords[1]]

                    # 检查调整是否合理
                    if abs(target_x - current_coords[0]) <= self.tolerance:
                        self.conservative_update_point(cell_idx, point_name, new_coords)

    def correct_shared_point_angles(self):
        """
        校正共享点的角度一致性，减少小角度分叉
        """
        if not self.angle_correction:
            return

        shared_points = self.find_shared_points()
        corrected_count = 0

        print(f"  发现 {len(shared_points)} 个共享角点")

        for coord_key, point_info in shared_points.items():
            if len(point_info) < 2:
                continue

            # 收集从该点出发的所有线条
            horizontal_lines = []
            vertical_lines = []

            for info in point_info:
                cell = self.cells[info['cell_idx']]
                bbox = cell['bbox']
                point_name = info['point_name']
                coords = info['coords']

                # 找到从该点出发的线条
                connected_points = self._get_connected_points(point_name, bbox)

                for connected_point, direction in connected_points:
                    if connected_point in bbox:
                        target_coords = bbox[connected_point]
                        angle = self.calculate_line_angle(coords, target_coords)

                        line_info = {
                            'cell_idx': info['cell_idx'],
                            'start_point': point_name,
                            'end_point': connected_point,
                            'start_coords': coords,
                            'end_coords': target_coords,
                            'angle': angle,
                            'length': math.sqrt((target_coords[0] - coords[0])**2 +
                                              (target_coords[1] - coords[1])**2)
                        }

                        if direction == 'horizontal':
                            horizontal_lines.append(line_info)
                        else:
                            vertical_lines.append(line_info)

            # 校正水平线角度一致性
            if len(horizontal_lines) > 1:
                corrected_count += self._align_lines_angle(horizontal_lines, 'horizontal')

            # 校正垂直线角度一致性
            if len(vertical_lines) > 1:
                corrected_count += self._align_lines_angle(vertical_lines, 'vertical')

        if corrected_count > 0:
            print(f"  校正了 {corrected_count} 条线的角度分叉")

    def _get_connected_points(self, point_name: str, bbox: Dict) -> List[Tuple[str, str]]:
        """
        获取与指定点连接的其他点

        Args:
            point_name: 点名称
            bbox: 边框信息

        Returns:
            连接点列表，每个元素为(点名称, 方向)
        """
        connections = {
            'p1': [('p2', 'horizontal'), ('p4', 'vertical')],
            'p2': [('p1', 'horizontal'), ('p3', 'vertical')],
            'p3': [('p2', 'vertical'), ('p4', 'horizontal')],
            'p4': [('p3', 'horizontal'), ('p1', 'vertical')]
        }

        return connections.get(point_name, [])

    def _align_lines_angle(self, lines: List[Dict], direction: str) -> int:
        """
        对齐同方向线条的角度

        Args:
            lines: 线条列表
            direction: 方向（'horizontal' 或 'vertical'）

        Returns:
            校正的线条数量
        """
        if len(lines) < 2:
            return 0

        # 计算所有线条的角度
        angles = [line['angle'] for line in lines]

        # 计算目标角度
        if direction == 'horizontal':
            # 水平线应该接近0度或180度
            target_angle = 0.0
            # 如果大部分角度接近180度，使用180度
            if sum(1 for a in angles if abs(a) > 90) > len(angles) / 2:
                target_angle = 180.0
        else:
            # 垂直线应该接近90度或-90度
            target_angle = 90.0
            # 如果大部分角度为负，使用-90度
            if sum(1 for a in angles if a < 0) > len(angles) / 2:
                target_angle = -90.0

        # 校正每条线的终点
        corrected_count = 0
        for line_info in lines:
            current_angle = line_info['angle']
            angle_diff = abs(current_angle - target_angle)

            # 处理角度跨越180度的情况
            if angle_diff > 180:
                angle_diff = 360 - angle_diff

            if angle_diff > self.angle_tolerance:
                # 重新计算终点坐标
                start_coords = line_info['start_coords']
                length = line_info['length']

                target_angle_rad = target_angle * math.pi / 180
                new_end_x = start_coords[0] + length * math.cos(target_angle_rad)
                new_end_y = start_coords[1] + length * math.sin(target_angle_rad)

                # 更新坐标
                cell = self.cells[line_info['cell_idx']]
                cell['bbox'][line_info['end_point']] = [new_end_x, new_end_y]
                corrected_count += 1

        return corrected_count

    def detect_angle_bifurcation(self) -> List[Dict]:
        """
        检测角度分叉问题

        Returns:
            分叉点信息列表
        """
        bifurcation_points = []
        shared_points = self.find_shared_points()

        for coord_key, point_info in shared_points.items():
            if len(point_info) < 2:
                continue

            # 收集从该点出发的所有线条
            outgoing_lines = []

            for info in point_info:
                cell = self.cells[info['cell_idx']]
                bbox = cell['bbox']
                point_name = info['point_name']
                coords = info['coords']

                # 找到从该点出发的线条
                connected_points = self._get_connected_points(point_name, bbox)

                for connected_point, direction in connected_points:
                    if connected_point in bbox:
                        target_coords = bbox[connected_point]
                        angle = self.calculate_line_angle(coords, target_coords)

                        outgoing_lines.append({
                            'cell_idx': info['cell_idx'],
                            'start_point': point_name,
                            'end_point': connected_point,
                            'start_coords': coords,
                            'end_coords': target_coords,
                            'angle': angle,
                            'direction': direction,
                            'length': math.sqrt((target_coords[0] - coords[0])**2 +
                                              (target_coords[1] - coords[1])**2)
                        })

            # 检查是否存在角度分叉
            if len(outgoing_lines) > 1:
                bifurcation_info = self._analyze_bifurcation(coord_key, outgoing_lines)
                if bifurcation_info['has_bifurcation']:
                    bifurcation_points.append(bifurcation_info)

        return bifurcation_points

    def _analyze_bifurcation(self, coord_key: str, lines: List[Dict]) -> Dict:
        """
        分析角度分叉情况

        Args:
            coord_key: 坐标键
            lines: 线条列表

        Returns:
            分叉分析结果
        """
        # 按方向分组
        horizontal_lines = [l for l in lines if l['direction'] == 'horizontal']
        vertical_lines = [l for l in lines if l['direction'] == 'vertical']

        bifurcation_info = {
            'coord_key': coord_key,
            'has_bifurcation': False,
            'horizontal_bifurcation': False,
            'vertical_bifurcation': False,
            'horizontal_lines': horizontal_lines,
            'vertical_lines': vertical_lines,
            'max_angle_diff': 0.0
        }

        # 检查水平线分叉
        if len(horizontal_lines) > 1:
            h_angles = [l['angle'] for l in horizontal_lines]
            h_angle_diff = max(h_angles) - min(h_angles)

            # 处理跨越180度的情况
            if h_angle_diff > 180:
                h_angle_diff = 360 - h_angle_diff

            if h_angle_diff > self.angle_tolerance:
                bifurcation_info['horizontal_bifurcation'] = True
                bifurcation_info['has_bifurcation'] = True
                bifurcation_info['max_angle_diff'] = max(bifurcation_info['max_angle_diff'], h_angle_diff)

        # 检查垂直线分叉
        if len(vertical_lines) > 1:
            v_angles = [l['angle'] for l in vertical_lines]
            v_angle_diff = max(v_angles) - min(v_angles)

            # 处理跨越180度的情况
            if v_angle_diff > 180:
                v_angle_diff = 360 - v_angle_diff

            if v_angle_diff > self.angle_tolerance:
                bifurcation_info['vertical_bifurcation'] = True
                bifurcation_info['has_bifurcation'] = True
                bifurcation_info['max_angle_diff'] = max(bifurcation_info['max_angle_diff'], v_angle_diff)

        return bifurcation_info

    def correct_angle_bifurcation(self, bifurcation_points: List[Dict]) -> int:
        """
        校正角度分叉

        Args:
            bifurcation_points: 分叉点列表

        Returns:
            校正的线条数量
        """
        corrected_count = 0

        for bifurcation in bifurcation_points:
            # 校正水平线分叉
            if bifurcation['horizontal_bifurcation']:
                corrected_count += self._correct_direction_bifurcation(
                    bifurcation['horizontal_lines'], 'horizontal'
                )

            # 校正垂直线分叉
            if bifurcation['vertical_bifurcation']:
                corrected_count += self._correct_direction_bifurcation(
                    bifurcation['vertical_lines'], 'vertical'
                )

        return corrected_count

    def _correct_direction_bifurcation(self, lines: List[Dict], direction: str) -> int:
        """
        校正特定方向的角度分叉

        Args:
            lines: 线条列表
            direction: 方向

        Returns:
            校正的线条数量
        """
        if len(lines) < 2:
            return 0

        # 计算目标角度
        angles = [l['angle'] for l in lines]

        if direction == 'horizontal':
            # 水平线应该接近0度或180度
            # 选择最接近的标准角度
            target_candidates = [0.0, 180.0]
            target_angle = min(target_candidates,
                             key=lambda x: min(abs(a - x) for a in angles))
        else:
            # 垂直线应该接近90度或-90度
            target_candidates = [90.0, -90.0]
            target_angle = min(target_candidates,
                             key=lambda x: min(abs(a - x) for a in angles))

        # 如果启用严格角度校正，使用角度吸附
        if self.strict_angle_correction:
            target_angle = self._snap_to_standard_angle(target_angle, direction)

        # 校正每条线
        corrected_count = 0
        for line in lines:
            current_angle = line['angle']
            angle_diff = abs(current_angle - target_angle)

            # 处理跨越180度的情况
            if angle_diff > 180:
                angle_diff = 360 - angle_diff

            if angle_diff > self.angle_tolerance:
                # 重新计算终点坐标
                start_coords = line['start_coords']
                length = line['length']

                target_angle_rad = target_angle * math.pi / 180
                new_end_x = start_coords[0] + length * math.cos(target_angle_rad)
                new_end_y = start_coords[1] + length * math.sin(target_angle_rad)

                # 更新坐标
                cell = self.cells[line['cell_idx']]
                cell['bbox'][line['end_point']] = [new_end_x, new_end_y]
                corrected_count += 1

        return corrected_count

    def _snap_to_standard_angle(self, angle: float, direction: str) -> float:
        """
        将角度吸附到标准角度

        Args:
            angle: 当前角度
            direction: 方向

        Returns:
            吸附后的角度
        """
        if direction == 'horizontal':
            # 水平线吸附到0度或180度
            if abs(angle) < self.angle_snap_threshold:
                return 0.0
            elif abs(angle - 180) < self.angle_snap_threshold:
                return 180.0
            elif abs(angle + 180) < self.angle_snap_threshold:
                return -180.0
        else:
            # 垂直线吸附到90度或-90度
            if abs(angle - 90) < self.angle_snap_threshold:
                return 90.0
            elif abs(angle + 90) < self.angle_snap_threshold:
                return -90.0

        return angle

    def improved_angle_correction(self):
        """
        执行改进的角度校正
        """
        if not self.angle_correction:
            return

        print("  执行改进的角度校正...")

        # 1. 检测角度分叉
        bifurcation_points = self.detect_angle_bifurcation()

        if not bifurcation_points:
            print("    未发现角度分叉问题")
            return

        print(f"    发现 {len(bifurcation_points)} 个角度分叉点")

        # 2. 校正角度分叉
        corrected_count = self.correct_angle_bifurcation(bifurcation_points)

        print(f"    校正了 {corrected_count} 条线的角度分叉")

        # 3. 如果启用严格模式，进行全局角度标准化
        if self.strict_angle_correction:
            global_corrected = self._global_angle_standardization()
            print(f"    全局角度标准化校正了 {global_corrected} 条线")

    def _global_angle_standardization(self) -> int:
        """
        全局角度标准化

        Returns:
            校正的线条数量
        """
        corrected_count = 0

        for cell in self.cells:
            bbox = cell['bbox']
            if not all(p in bbox for p in ['p1', 'p2', 'p3', 'p4']):
                continue

            # 处理四条边
            edges = [
                ('p1', 'p2', 'horizontal'),  # 上边
                ('p2', 'p3', 'vertical'),    # 右边
                ('p3', 'p4', 'horizontal'),  # 下边
                ('p4', 'p1', 'vertical')     # 左边
            ]

            for start_point, end_point, direction in edges:
                start_coords = bbox[start_point]
                end_coords = bbox[end_point]

                current_angle = self.calculate_line_angle(start_coords, end_coords)
                target_angle = self._snap_to_standard_angle(current_angle, direction)

                angle_diff = abs(current_angle - target_angle)
                if angle_diff > 180:
                    angle_diff = 360 - angle_diff

                if angle_diff > self.angle_tolerance:
                    # 重新计算终点
                    length = math.sqrt((end_coords[0] - start_coords[0])**2 +
                                     (end_coords[1] - start_coords[1])**2)

                    target_angle_rad = target_angle * math.pi / 180
                    new_end_x = start_coords[0] + length * math.cos(target_angle_rad)
                    new_end_y = start_coords[1] + length * math.sin(target_angle_rad)

                    bbox[end_point] = [new_end_x, new_end_y]
                    corrected_count += 1

        return corrected_count

    def _evaluate_optimization_quality(self):
        """
        评估优化质量
        """
        try:
            # 计算边界对齐质量
            horizontal_errors = self._calculate_boundary_errors('horizontal')
            vertical_errors = self._calculate_boundary_errors('vertical')

            if horizontal_errors:
                h_avg = sum(horizontal_errors) / len(horizontal_errors)
                h_max = max(horizontal_errors)
                print(f"  水平对齐质量: 平均误差 {h_avg:.2f}px, 最大误差 {h_max:.2f}px")

            if vertical_errors:
                v_avg = sum(vertical_errors) / len(vertical_errors)
                v_max = max(vertical_errors)
                print(f"  垂直对齐质量: 平均误差 {v_avg:.2f}px, 最大误差 {v_max:.2f}px")

        except Exception as e:
            print(f"  质量评估失败: {e}")

    def _calculate_boundary_errors(self, direction: str) -> List[float]:
        """
        计算边界对齐误差

        Args:
            direction: 'horizontal' 或 'vertical'

        Returns:
            误差列表
        """
        errors = []

        try:
            if direction == 'horizontal':
                # 检查水平边界的对齐误差
                for row in set(r for r, c in self.grid_structure.keys()):
                    row_cells = [cell for (r, c), cells in self.grid_structure.items()
                               if r == row for cell in cells]
                    if len(row_cells) > 1:
                        # 检查同一行单元格的上下边界对齐
                        top_coords = [cell['bbox']['p1'][1] for cell in row_cells] + \
                                   [cell['bbox']['p2'][1] for cell in row_cells]
                        bottom_coords = [cell['bbox']['p3'][1] for cell in row_cells] + \
                                      [cell['bbox']['p4'][1] for cell in row_cells]

                        if len(top_coords) > 1:
                            errors.append(max(top_coords) - min(top_coords))
                        if len(bottom_coords) > 1:
                            errors.append(max(bottom_coords) - min(bottom_coords))

            elif direction == 'vertical':
                # 检查垂直边界的对齐误差
                for col in set(c for r, c in self.grid_structure.keys()):
                    col_cells = [cell for (r, c), cells in self.grid_structure.items()
                               if c == col for cell in cells]
                    if len(col_cells) > 1:
                        # 检查同一列单元格的左右边界对齐
                        left_coords = [cell['bbox']['p1'][0] for cell in col_cells] + \
                                    [cell['bbox']['p4'][0] for cell in col_cells]
                        right_coords = [cell['bbox']['p2'][0] for cell in col_cells] + \
                                     [cell['bbox']['p3'][0] for cell in col_cells]

                        if len(left_coords) > 1:
                            errors.append(max(left_coords) - min(left_coords))
                        if len(right_coords) > 1:
                            errors.append(max(right_coords) - min(right_coords))

        except Exception:
            pass  # 忽略计算错误，返回空列表

        return errors


    def optimize_alignment(self):
        """执行透视感知的角点对齐优化，重点加强共享点对齐"""
        print("构建网格结构...")
        self.build_grid_structure()

        # 第一步：强制性共享点对齐（最重要）
        print("强制性共享点对齐...")
        self.force_shared_point_alignment()

        print("对齐行边界...")
        self.align_row_boundaries()

        print("对齐列边界...")
        self.align_column_boundaries()

        # 第二步：再次强制共享点对齐
        print("再次强制共享点对齐...")
        self.force_shared_point_alignment()

        print("微调相近角点...")
        self.fine_tune_nearby_points()

        # 第三步：最终强制共享点对齐
        print("最终强制共享点对齐...")
        self.force_shared_point_alignment()

        # 精确共享点对齐（新增）
        print("精确对齐共享角点...")
        self.enforce_grid_consistency()

        # 角度校正步骤
        if self.angle_correction:
            print("校正角度一致性...")
            self.correct_shared_point_angles()

        print("透视感知优化完成！")

        # 评估优化质量
        self._evaluate_optimization_quality()

    def copy_image_file(self, image_file: str, output_dir: str) -> bool:
        """
        复制图片文件到输出目录，处理EXIF旋转信息避免旋转bug

        Args:
            image_file: 原图片文件路径
            output_dir: 输出目录路径

        Returns:
            复制是否成功
        """
        try:
            from PIL import Image, ImageOps
            import shutil

            image_path = Path(image_file)
            output_image_path = Path(output_dir) / image_path.name

            # 确保输出目录存在
            output_image_path.parent.mkdir(parents=True, exist_ok=True)

            # 检查是否是图片文件，如果是则处理EXIF旋转
            image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif'}
            if image_path.suffix.lower() in image_extensions:
                try:
                    # 使用PIL打开图片并自动处理EXIF旋转
                    with Image.open(image_file) as img:
                        # 使用ImageOps.exif_transpose自动处理EXIF旋转信息
                        img_corrected = ImageOps.exif_transpose(img)

                        # 如果图片被旋转了，保存修正后的图片
                        if img_corrected is not img:
                            # 保持原始质量和格式
                            save_kwargs = {}
                            if img.format == 'JPEG':
                                save_kwargs['quality'] = 95
                                save_kwargs['optimize'] = True
                            elif img.format == 'PNG':
                                save_kwargs['optimize'] = True

                            img_corrected.save(output_image_path, format=img.format, **save_kwargs)
                            print(f"    🔄 已修正图片EXIF旋转: {image_path.name}")
                        else:
                            # 没有旋转问题，直接复制
                            shutil.copy2(image_file, output_image_path)

                    return True

                except Exception as img_error:
                    print(f"    ⚠️  PIL处理失败，使用直接复制: {img_error}")
                    # 如果PIL处理失败，回退到直接复制
                    shutil.copy2(image_file, output_image_path)
                    return True
            else:
                # 非图片文件直接复制
                shutil.copy2(image_file, output_image_path)
                return True

        except Exception as e:
            print(f"复制图片文件失败 {image_file}: {e}")
            return False

    def force_shared_point_alignment(self):
        """
        强制性共享点对齐，确保相邻单元格的角点使用完全相同的坐标
        """
        print("    执行强制性共享点对齐...")

        # 多次迭代确保完全对齐
        max_iterations = 3
        for iteration in range(max_iterations):
            print(f"    第 {iteration + 1} 次迭代...")

            shared_points = self.find_shared_points()
            if not shared_points:
                print("    未发现需要对齐的共享角点")
                break

            aligned_groups = 0
            total_aligned_points = 0

            for _, point_group in shared_points.items():
                if len(point_group) < 2:
                    continue

                # 计算最优中心坐标
                center_coords = self._calculate_optimal_center(point_group)

                # 强制所有点使用相同坐标
                for point in point_group:
                    self._force_update_point(
                        point['cell_idx'],
                        point['point_name'],
                        center_coords
                    )
                    total_aligned_points += 1

                aligned_groups += 1

            print(f"    第 {iteration + 1} 次迭代对齐了 {aligned_groups} 组共享点，共 {total_aligned_points} 个角点")

            # 如果没有需要对齐的点，提前退出
            if aligned_groups == 0:
                break

    def save_optimized_annotation(self, output_path: str):
        """
        保存优化后的标注文件，保持原有属性不变

        Args:
            output_path: 输出文件路径
        """
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

        # 基于原始数据创建输出数据，保护所有原始属性
        output_data = self.original_data.copy()

        # 更新优化后的单元格数据
        output_data['cells'] = self.cells

        # 确保基本属性正确
        output_data['table_ind'] = self.table_ind
        output_data['image_path'] = self.image_path

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)

        print(f"优化后的标注文件已保存到: {output_path}")

    def optimize_table_annotation(self, annotation_file: str, output_file: Optional[str] = None,
                                image_file: Optional[str] = None) -> Dict[str, Any]:
        """
        优化表格标注文件的主入口方法

        Args:
            annotation_file: 输入的标注文件路径
            output_file: 输出文件路径，如果为None则覆盖原文件
            image_file: 对应的图片文件路径，用于自适应阈值计算

        Returns:
            包含优化结果的字典
        """
        try:
            # 加载标注文件
            self.load_annotation(annotation_file, image_file)

            # 执行优化
            self.optimize_alignment()

            # 保存结果
            if output_file is None:
                output_file = annotation_file

            self.save_optimized_annotation(output_file)

            return {
                'success': True,
                'input_file': annotation_file,
                'output_file': output_file,
                'cell_count': len(self.cells),
                'adaptive_threshold': self.tolerance if self.adaptive_threshold else None,
                'image_info': self.image_info
            }

        except Exception as e:
            return {
                'success': False,
                'input_file': annotation_file,
                'output_file': output_file or annotation_file,
                'error': str(e)
            }


class BatchProcessor:
    """批量处理器类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化批量处理器

        Args:
            config: 配置字典
        """
        self.config = config

    def find_annotation_files(self, input_dir: str) -> List[Path]:
        """查找所有标注文件"""
        input_path = Path(input_dir)
        pattern = self.config.get('annotation_pattern', '*_table_annotation.json')
        return list(input_path.glob(pattern))

    def find_corresponding_image(self, annotation_file: Path) -> Optional[str]:
        """查找对应的图片文件"""
        base_name = annotation_file.stem.replace('_table_annotation', '')
        image_dir = annotation_file.parent

        image_extensions = self.config.get('image_extensions', ['.jpg', '.jpeg', '.png', '.bmp'])

        for ext in image_extensions:
            image_path = image_dir / f"{base_name}{ext}"
            if image_path.exists():
                return str(image_path)

        return None

    def copy_image_file(self, image_file: str, output_dir: str) -> bool:
        """
        复制图片文件到输出目录，处理EXIF旋转信息避免旋转bug

        Args:
            image_file: 原图片文件路径
            output_dir: 输出目录路径

        Returns:
            复制是否成功
        """
        try:
            from PIL import Image, ImageOps
            import shutil

            image_path = Path(image_file)
            output_image_path = Path(output_dir) / image_path.name

            # 确保输出目录存在
            output_image_path.parent.mkdir(parents=True, exist_ok=True)

            # 检查是否是图片文件，如果是则处理EXIF旋转
            image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif'}
            if image_path.suffix.lower() in image_extensions:
                try:
                    # 使用PIL打开图片并自动处理EXIF旋转
                    with Image.open(image_file) as img:
                        # 使用ImageOps.exif_transpose自动处理EXIF旋转信息
                        img_corrected = ImageOps.exif_transpose(img)

                        # 如果图片被旋转了，保存修正后的图片
                        if img_corrected is not img:
                            # 保持原始质量和格式
                            save_kwargs = {}
                            if img.format == 'JPEG':
                                save_kwargs['quality'] = 95
                                save_kwargs['optimize'] = True
                            elif img.format == 'PNG':
                                save_kwargs['optimize'] = True

                            img_corrected.save(output_image_path, format=img.format, **save_kwargs)
                            print(f"    🔄 已修正图片EXIF旋转: {image_path.name}")
                        else:
                            # 没有旋转问题，直接复制
                            shutil.copy2(image_file, output_image_path)

                    return True

                except Exception as img_error:
                    print(f"    ⚠️  PIL处理失败，使用直接复制: {img_error}")
                    # 如果PIL处理失败，回退到直接复制
                    shutil.copy2(image_file, output_image_path)
                    return True
            else:
                # 非图片文件直接复制
                shutil.copy2(image_file, output_image_path)
                return True

        except Exception as e:
            print(f"复制图片文件失败 {image_file}: {e}")
            return False

    def process_batch(self) -> Dict[str, Any]:
        """执行批量处理"""
        input_dir = self.config['input_dir']
        output_dir = self.config['output_dir']
        max_workers = self.config.get('max_workers', 4)

        # 查找所有标注文件
        annotation_files = self.find_annotation_files(input_dir)

        if not annotation_files:
            return {
                'success': False,
                'error': f'在 {input_dir} 中未找到标注文件'
            }

        print(f"找到 {len(annotation_files)} 个标注文件")
        print(f"输出目录: {output_dir}")
        print(f"并行处理数: {max_workers}")
        print("=" * 60)

        # 统计信息
        stats = {
            'total_files': len(annotation_files),
            'successful': 0,
            'failed': 0,
            'images_copied': 0,
            'images_failed': 0,
            'total_time': 0,
            'results': []
        }

        start_time = time.time()

        # 并行处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            future_to_file = {}
            for annotation_file in annotation_files:
                # 确定输出文件路径
                relative_path = annotation_file.relative_to(input_dir)
                output_file = Path(output_dir) / relative_path

                # 查找对应图片
                image_file = self.find_corresponding_image(annotation_file)

                # 创建优化器实例
                optimizer = SimplifiedTableOptimizer(
                    tolerance=self.config.get('tolerance', 3.0),
                    adaptive_threshold=self.config.get('adaptive_threshold', True),
                    merge_threshold_factor=self.config.get('merge_threshold_factor', 1.5),
                    alignment_strength=self.config.get('alignment_strength', 0.8),
                    preserve_perspective=self.config.get('preserve_perspective', True)
                )

                future = executor.submit(
                    optimizer.optimize_table_annotation,
                    str(annotation_file),
                    str(output_file),
                    image_file
                )
                future_to_file[future] = annotation_file

            # 处理结果
            for i, future in enumerate(as_completed(future_to_file), 1):
                annotation_file = future_to_file[future]
                result = future.result()

                stats['results'].append(result)

                if result['success']:
                    stats['successful'] += 1
                    print(f"[{i:3d}/{len(annotation_files)}] ✅ {annotation_file.name}")
                    if 'adaptive_threshold' in result and result['adaptive_threshold']:
                        print(f"    自适应阈值: {result['adaptive_threshold']:.2f}")

                    # 复制对应的图片文件（如果启用）
                    if self.config.get('copy_images', True):
                        image_file = self.find_corresponding_image(annotation_file)
                        if image_file:
                            if self.copy_image_file(image_file, output_dir):
                                stats['images_copied'] += 1
                                print(f"    📷 图片已复制: {Path(image_file).name}")
                            else:
                                stats['images_failed'] += 1
                                print(f"    ❌ 图片复制失败: {Path(image_file).name}")
                        else:
                            stats['images_failed'] += 1
                            print(f"    ⚠️  未找到对应图片")
                else:
                    stats['failed'] += 1
                    print(f"[{i:3d}/{len(annotation_files)}] ❌ {annotation_file.name} - {result['error']}")

        stats['total_time'] = time.time() - start_time

        # 输出统计
        print("\n" + "=" * 60)
        print("批量处理完成")
        print("=" * 60)
        print(f"📄 标注文件:")
        print(f"   总文件数: {stats['total_files']}")
        print(f"   成功处理: {stats['successful']}")
        print(f"   处理失败: {stats['failed']}")
        print(f"   成功率: {stats['successful']/stats['total_files']*100:.1f}%")
        print(f"📷 图片文件:")
        print(f"   成功复制: {stats['images_copied']}")
        print(f"   复制失败: {stats['images_failed']}")
        if stats['images_copied'] + stats['images_failed'] > 0:
            print(f"   复制成功率: {stats['images_copied']/(stats['images_copied']+stats['images_failed'])*100:.1f}%")
        print(f"⏱️  总处理时间: {stats['total_time']:.1f}秒")

        return stats


# ==================== 默认配置区域 ====================
# 注意：主配置在文件顶部的 CONFIG 字典中，这里只是作为命令行参数的默认值
# ================================================


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='表格标注优化器 - 使用透视感知算法优化表格角点对齐',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python table_annotation_optimizer.py
  python table_annotation_optimizer.py --input-dir my_data --output-dir my_output
  python table_annotation_optimizer.py --tolerance 5.0 --workers 8
  python table_annotation_optimizer.py --no-adaptive --no-perspective
        """
    )

    parser.add_argument('--input-dir', help='输入目录路径（默认: borderless_table）')
    parser.add_argument('--output-dir', help='输出目录路径（默认: borderless_table_optimized）')
    parser.add_argument('--tolerance', type=float, help='基础容差阈值，单位像素（默认: 3.0）')
    parser.add_argument('--workers', type=int, help='并行处理线程数（默认: 4）')
    parser.add_argument('--no-perspective', action='store_true', help='不保持透视变换，强制矩形对齐')
    parser.add_argument('--no-adaptive', action='store_true', help='不使用自适应阈值，使用固定阈值')
    parser.add_argument('--no-copy-images', action='store_true', help='不复制图片文件到输出目录')
    parser.add_argument('--pattern', help='标注文件匹配模式（默认: *_table_annotation.json）')

    args = parser.parse_args()

    # 更新配置（使用顶部的 CONFIG 作为基础配置）
    config = CONFIG.copy()
    if args.input_dir:
        config['input_dir'] = args.input_dir
    if args.output_dir:
        config['output_dir'] = args.output_dir
    if args.tolerance:
        config['tolerance'] = args.tolerance
    if args.workers:
        config['max_workers'] = args.workers
    if args.no_perspective:
        config['preserve_perspective'] = False
    if args.no_adaptive:
        config['adaptive_threshold'] = False
    if args.no_copy_images:
        config['copy_images'] = False
    if args.pattern:
        config['annotation_pattern'] = args.pattern

    # 检查输入目录
    if not os.path.exists(config['input_dir']):
        print(f"❌ 错误: 输入目录 {config['input_dir']} 不存在")
        return 1

    print("=" * 60)
    print("🔧 表格标注优化器 v3.0")
    print("=" * 60)
    print(f"📁 输入目录: {config['input_dir']}")
    print(f"📁 输出目录: {config['output_dir']}")
    print(f"🎯 基础阈值: {config['tolerance']} 像素")
    print(f"🔄 保持透视: {'是' if config['preserve_perspective'] else '否'}")
    print(f"📊 自适应阈值: {'是' if config['adaptive_threshold'] else '否'}")
    print(f"📷 复制图片: {'是' if config.get('copy_images', True) else '否'}")
    print(f"⚡ 并行线程: {config['max_workers']}")
    print(f"🔍 文件模式: {config['annotation_pattern']}")
    print()

    # 执行批量处理
    processor = BatchProcessor(config)
    stats = processor.process_batch()

    if stats.get('success', True) and stats['failed'] == 0:
        print(f"\n✅ 处理完成！优化后的文件保存在: {config['output_dir']}")
        print(f"📊 处理统计: {stats['successful']}/{stats['total_files']} 成功")
        return 0
    else:
        print(f"\n❌ 处理过程中出现错误")
        if 'error' in stats:
            print(f"错误信息: {stats['error']}")
        return 1


if __name__ == "__main__":
    exit(main())
