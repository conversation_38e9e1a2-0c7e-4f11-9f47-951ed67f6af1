#!/usr/bin/env python3
"""
简单测试copy_image_file方法是否可用
"""

from table_annotation_optimizer import PerspectiveAwareOptimizer

def test_copy_method():
    optimizer = PerspectiveAwareOptimizer()
    
    # 检查方法是否存在
    if hasattr(optimizer, 'copy_image_file'):
        print("✅ copy_image_file 方法存在")
        print(f"方法类型: {type(optimizer.copy_image_file)}")
    else:
        print("❌ copy_image_file 方法不存在")
        print("可用方法:")
        methods = [method for method in dir(optimizer) if not method.startswith('_')]
        for method in methods:
            print(f"  - {method}")

if __name__ == "__main__":
    test_copy_method()
